{"files.associations": {"usart.h": "c", "scheduler.h": "c", "gpio.h": "c", "main.h": "c", "mydefine.h": "c", "stdio.h": "c", "uart_driver.h": "c", "dma.h": "c", "oled.h": "c", "xutility": "c", "string.h": "c", "ringbuffer.h": "c", "hwt101_driver.h": "c", "uart_app.h": "c", "i2c.h": "c", "oled_app.h": "c", "stdarg.h": "c", "hardware_iic.h": "c", "oled_driver.h": "c", "scheduler_task.h": "c", "tim.h": "c", "stdbool.h": "c", "gray_app.h": "c", "atomic": "c", "compare": "c", "cstddef": "c", "limits": "c", "memory": "c", "type_traits": "c", "xmemory": "c", "xstring": "c", "math.h": "c", "motor_driver.h": "c", "encoder_driver.h": "c", "encoder_app.h": "c", "motor_app.h": "c", "pid.h": "c", "ebtn.h": "c", "led_driver.h": "c", "key_driver.h": "c", "key_app.h": "c", "led_app.h": "c", "pid_app.h": "c", "ws_protocol_parser.h": "c", "stdint.h": "c", "step_motor_app.h": "c", "stdlib.h": "c", "step_motor.h": "c"}}