*** Using Compiler 'V6.23', folder: 'D:\Keil_v5\ARM\ARMCLANG\Bin'
Build target '1.4_USART'
compiling encoder_driver.c...
compiling encoder_app.c...
armclang: error: no such file or directory: '../Driver/oled_driver.c'
armclang: error: no input files
compiling oled_driver.c...
compiling key_driver.c...
compiling led_app.c...
compiling led_driver.c...
compiling motor_driver.c...
compiling gray_app.c...
compiling key_app.c...
compiling step_motor.c...
compiling uart_app.c...
C:/Users/<USER>/Desktop/1.6_Motor+Encode+PID/1.6_Motor+Encode+PID/Icode/oled_app.c(14): error: call to undeclared function 'Oled_Printf'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   14 |   Oled_Printf(0, 0, 12, 0, "OLED Init OK");
      |   ^
C:/Users/<USER>/Desktop/1.6_Motor+Encode+PID/1.6_Motor+Encode+PID/Icode/oled_app.c(14): note: did you mean 'oled_printf'?
../Icode\oled_app.h(9): note: 'oled_printf' declared here
    9 | int oled_printf(uint8_t x, uint8_t y, const char *format, ...);
      |     ^
C:/Users/<USER>/Desktop/1.6_Motor+Encode+PID/1.6_Motor+Encode+PID/Icode/oled_app.c(44): error: call to undeclared function 'Oled_Printf'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   44 |             Oled_Printf(0, 0, 12, 0, "HWT101 GYRO");
      |             ^
C:/Users/<USER>/Desktop/1.6_Motor+Encode+PID/1.6_Motor+Encode+PID/Icode/oled_app.c(52): error: call to undeclared function 'Oled_Printf'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   52 |             Oled_Printf(0, 0, 12, 0, "HWT101 GYRO");
      |             ^
3 errors generated.
compiling oled_app.c...
compiling Scheduler.c...
compiling pid_app.c...
compiling step_motor_app.c...
compiling motor_app.c...
compiling Scheduler_Task.c...
"1.4_USART\1.4_USART" - 5 Error(s), 0 Warning(s).
Target not created.
Build Time Elapsed:  00:00:50
