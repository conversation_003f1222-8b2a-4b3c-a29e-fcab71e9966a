--cpu=Cortex-M4.fp.sp
"1.4_usart\startup_stm32f407xx.o"
"1.4_usart\main.o"
"1.4_usart\gpio.o"
"1.4_usart\dma.o"
"1.4_usart\i2c.o"
"1.4_usart\tim.o"
"1.4_usart\usart.o"
"1.4_usart\stm32f4xx_it.o"
"1.4_usart\stm32f4xx_hal_msp.o"
"1.4_usart\stm32f4xx_hal_i2c.o"
"1.4_usart\stm32f4xx_hal_i2c_ex.o"
"1.4_usart\stm32f4xx_hal_rcc.o"
"1.4_usart\stm32f4xx_hal_rcc_ex.o"
"1.4_usart\stm32f4xx_hal_flash.o"
"1.4_usart\stm32f4xx_hal_flash_ex.o"
"1.4_usart\stm32f4xx_hal_flash_ramfunc.o"
"1.4_usart\stm32f4xx_hal_gpio.o"
"1.4_usart\stm32f4xx_hal_dma_ex.o"
"1.4_usart\stm32f4xx_hal_dma.o"
"1.4_usart\stm32f4xx_hal_pwr.o"
"1.4_usart\stm32f4xx_hal_pwr_ex.o"
"1.4_usart\stm32f4xx_hal_cortex.o"
"1.4_usart\stm32f4xx_hal.o"
"1.4_usart\stm32f4xx_hal_exti.o"
"1.4_usart\stm32f4xx_hal_tim.o"
"1.4_usart\stm32f4xx_hal_tim_ex.o"
"1.4_usart\stm32f4xx_hal_uart.o"
"1.4_usart\system_stm32f4xx.o"
"1.4_usart\ringbuffer.o"
"1.4_usart\ws_protocol_parser.o"
"1.4_usart\oled.o"
"1.4_usart\hardware_iic.o"
"1.4_usart\ebtn.o"
"1.4_usart\pid.o"
"1.4_usart\hwt101_driver.o"
"1.4_usart\uart_driver.o"
"1.4_usart\oled_driver.o"
"1.4_usart\encoder_driver.o"
"1.4_usart\key_driver.o"
"1.4_usart\led_driver.o"
"1.4_usart\motor_driver.o"
"1.4_usart\step_motor.o"
"1.4_usart\uart_app.o"
"1.4_usart\oled_app.o"
"1.4_usart\oled_task.o"
"1.4_usart\gray_app.o"
"1.4_usart\encoder_app.o"
"1.4_usart\key_app.o"
"1.4_usart\led_app.o"
"1.4_usart\motor_app.o"
"1.4_usart\pid_app.o"
"1.4_usart\step_motor_app.o"
"1.4_usart\scheduler.o"
"1.4_usart\scheduler_task.o"
--strict --scatter "1.4_USART\1.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "1.map" -o 1.4_USART\1.4_USART