# OpenMV步进电机自动追踪控制系统集成文档

## 📋 项目概述

将OpenMV H7 Plus摄像头模块集成到STM32F407步进电机控制系统中，实现自动视觉追踪功能。OpenMV替代原来的UART5串口控制，通过检测矩形目标并自动发送步进电机控制指令，使矩形中心与屏幕中心十字重合。

## 🎯 核心功能

### 1. 视觉检测
- **目标检测**: 自动检测图像中的矩形目标
- **中心计算**: 精确计算矩形几何中心坐标
- **实时追踪**: 连续监控目标位置变化

### 2. 自动控制
- **偏差计算**: 计算目标中心与图像中心的偏差
- **智能分级**: 根据偏差距离自动选择步进级别
- **实时调整**: 自动发送控制指令调整步进电机位置

## ⚙️ 技术参数

### 图像参数
```python
IMG_CENTER_X = 160  # 图像中心X坐标 (320/2)
IMG_CENTER_Y = 120  # 图像中心Y坐标 (240/2)
```

### 控制参数
```python
TRACKING_DEADZONE = 8      # 死区范围（像素）
CONTROL_INTERVAL = 100     # 控制间隔（毫秒）
```

### 步进级别映射
```python
STEP_LEVEL_1 = 15   # 微调：偏差 < 15像素 → 0.3°
STEP_LEVEL_2 = 30   # 小步：偏差 < 30像素 → 0.6°
STEP_LEVEL_3 = 50   # 中步：偏差 < 50像素 → 0.9°
STEP_LEVEL_4 = 999  # 大步：偏差 >= 50像素 → 1.2°
```

## 📡 通信协议

### 指令格式
```
方向,级别\r\n
```

### 方向定义
```python
DIR_RIGHT = 1  # 右移 (X轴正方向)
DIR_LEFT = 2   # 左移 (X轴负方向)
DIR_DOWN = 3   # 下移 (Y轴负方向)
DIR_UP = 4     # 上移 (Y轴正方向)
```

### 指令示例
```
1,3\r\n  # 右移，中步(0.9°)
2,1\r\n  # 左移，微调(0.3°)
3,4\r\n  # 下移，大步(1.2°)
4,2\r\n  # 上移，小步(0.6°)
```

## 🔧 核心算法

### 1. 偏差计算
```python
def control_stepper_motor(target_x, target_y):
    # 计算偏差
    error_x = target_x - IMG_CENTER_X
    error_y = target_y - IMG_CENTER_Y
    
    # 计算偏差距离
    distance_x = abs(error_x)
    distance_y = abs(error_y)
```

### 2. 步进级别选择
```python
def calculate_step_level(distance):
    if distance < STEP_LEVEL_1:
        return 1  # 微调
    elif distance < STEP_LEVEL_2:
        return 2  # 小步
    elif distance < STEP_LEVEL_3:
        return 3  # 中步
    else:
        return 4  # 大步
```

### 3. 方向判断
```python
# X轴控制
if error_x > 0:
    send_motor_command(DIR_RIGHT, level_x)  # 目标在右侧，右移
else:
    send_motor_command(DIR_LEFT, level_x)   # 目标在左侧，左移

# Y轴控制
if error_y > 0:
    send_motor_command(DIR_DOWN, level_y)   # 目标在下方，下移
else:
    send_motor_command(DIR_UP, level_y)     # 目标在上方，上移
```

## 🎮 控制逻辑

### 1. 死区控制
- 当目标偏差在8像素范围内时，停止调整
- 避免系统震荡，提高稳定性

### 2. 分级控制
- 根据偏差距离自动选择合适的步进级别
- 远距离使用大步进，近距离使用小步进
- 实现快速接近 + 精确定位

### 3. 时间控制
- 控制指令发送间隔为100毫秒
- 避免指令过于频繁，确保电机有足够时间执行

## 📊 状态显示

### 实时信息显示
```python
# 偏差信息
img.draw_string(10, 30, "Error: X=%d Y=%d" % (error_x, error_y))

# 距离信息
img.draw_string(10, 50, "Distance: %d" % distance)

# 追踪状态
if distance <= TRACKING_DEADZONE:
    img.draw_string(10, 70, "Status: LOCKED", color=(0, 255, 0))
else:
    img.draw_string(10, 70, "Status: TRACKING", color=(255, 255, 0))
```

### 视觉标记
- **白色十字**: 图像中心标记
- **黄色圆圈**: 矩形中心标记
- **红色边框**: 检测到的矩形轮廓

## 🔄 工作流程

1. **图像采集**: OpenMV连续采集320x240分辨率图像
2. **目标检测**: 检测图像中的矩形目标
3. **中心计算**: 计算矩形几何中心坐标
4. **偏差分析**: 计算与图像中心的偏差
5. **级别选择**: 根据偏差距离选择步进级别
6. **指令发送**: 发送控制指令到STM32
7. **状态更新**: 更新显示信息，等待下一周期

## ✅ 集成完成状态

- ✅ **视觉检测**: 矩形目标检测和中心计算
- ✅ **自动控制**: 偏差计算和步进级别选择
- ✅ **通信协议**: 与STM32兼容的指令格式
- ✅ **实时显示**: 追踪状态和偏差信息显示
- ✅ **稳定控制**: 死区控制和时间间隔控制

## 🎯 预期效果

系统启动后，OpenMV将自动检测矩形目标，计算其与图像中心的偏差，并自动发送步进电机控制指令，使矩形中心逐步移动到图像中心位置，实现精确的视觉追踪对准功能。
