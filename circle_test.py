# 激光画圆测试 - 专门解决轨迹切换问题

import sensor, image, time, math
from pyb import UART

# 初始化传感器
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

# 初始化UART
uart = UART(3, 115200)

# 画圆参数
circle_radius_ratio = 0.3   # 圆半径占矩形短边的比例
circle_points = 8           # 减少到8个点，便于测试
current_target_index = 0    # 当前目标点索引
tracking_deadzone = 8       # 增大死区，更容易到达
control_delay = 300         # 控制间隔300ms
last_control_time = 0

# 强制切换参数
target_stay_time = 0        # 在当前目标点停留的时间
max_stay_time = 2000        # 最大停留时间(2秒)

def generate_circle_trajectory(center, radius, num_points):
    """生成圆形轨迹点"""
    trajectory = []
    cx, cy = center
    
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        trajectory.append((x, y))
    
    return trajectory

def detect_purple_laser(img):
    """检测紫色激光点"""
    purple_threshold = (20, 100, 10, 127, -128, -10)
    
    blobs = img.find_blobs([purple_threshold], 
                          pixels_threshold=10, 
                          area_threshold=10,
                          merge=True)
    
    laser_points = []
    for blob in blobs:
        if 10 <= blob.pixels() <= 500:
            laser_points.append((blob.cx(), blob.cy()))
            # 绘制激光点
            img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 255), size=15)
    
    return laser_points

def detect_target_areas(img):
    """简化的目标检测"""
    black_threshold = (0, 30, -128, 127, -128, 127)
    
    blobs = img.find_blobs([black_threshold],
                         pixels_threshold=500,
                         area_threshold=500,
                         merge=True)
    
    target_areas = []
    for blob in blobs:
        if 800 <= blob.pixels() <= 15000:
            target_areas.append(blob)
            # 绘制目标区域
            img.draw_rectangle(blob.rect(), color=(0, 255, 0), thickness=2)
    
    return target_areas

def get_control_command(target_pos, laser_pos, deadzone=8):
    """简化的控制指令计算"""
    if not target_pos or not laser_pos:
        return None, 0
    
    target_x, target_y = target_pos
    laser_x, laser_y = laser_pos
    
    error_x = target_x - laser_x
    error_y = target_y - laser_y
    
    # 简化的步进级别
    def get_step_level(error):
        abs_error = abs(error)
        if abs_error > 20:
            return 3  # 大步进
        elif abs_error > 10:
            return 2  # 中步进
        else:
            return 1  # 小步进
    
    # 优先处理X轴误差
    if abs(error_x) > deadzone:
        direction = "2" if error_x > 0 else "1"  # 左移 或 右移
        level = get_step_level(error_x)
        return direction, level
    elif abs(error_y) > deadzone:
        direction = "3" if error_y > 0 else "4"  # 下移 或 上移
        level = get_step_level(error_y)
        return direction, level
    else:
        return None, 0

print("激光画圆测试启动 - 8个轨迹点")
print("死区范围:", tracking_deadzone, "像素")
print("控制间隔:", control_delay, "ms")

frame_count = 0
circle_trajectory = []

while True:
    img = sensor.snapshot()
    frame_count += 1
    
    # 1. 检测目标区域
    target_areas = detect_target_areas(img)
    
    # 2. 生成圆形轨迹
    if target_areas:
        area = target_areas[0]
        center_x, center_y = area.cx(), area.cy()
        rect_w, rect_h = area.w(), area.h()
        
        # 计算圆半径
        short_side = min(rect_w, rect_h)
        circle_radius = int(short_side * circle_radius_ratio)
        
        # 生成轨迹
        circle_trajectory = generate_circle_trajectory(
            (center_x, center_y), circle_radius, circle_points
        )
        
        # 绘制轨迹点
        for i, (x, y) in enumerate(circle_trajectory):
            if i == current_target_index:
                # 当前目标点 - 大黄圈
                img.draw_circle(x, y, 12, color=(255, 255, 0), thickness=3)
                img.draw_string(x-15, y-25, str(i+1), color=(255, 255, 0))
            else:
                # 其他点 - 小灰圈
                img.draw_circle(x, y, 4, color=(128, 128, 128), thickness=1)
                img.draw_string(x-5, y-15, str(i+1), color=(128, 128, 128))
    
    # 3. 检测激光
    laser_points = detect_purple_laser(img)
    
    # 4. 画圆控制逻辑
    if circle_trajectory and laser_points:
        laser_pos = laser_points[0]
        laser_x, laser_y = laser_pos
        target_x, target_y = circle_trajectory[current_target_index]
        
        # 计算距离
        error_x = target_x - laser_x
        error_y = target_y - laser_y
        distance = math.sqrt(error_x*error_x + error_y*error_y)
        
        # 绘制连接线
        img.draw_line(laser_x, laser_y, target_x, target_y, color=(255, 255, 0), thickness=2)
        
        # 状态显示
        img.draw_string(10, 10, "Point: {}/{} Dist: {:.1f}".format(
            current_target_index+1, len(circle_trajectory), distance), color=(255, 255, 255))
        
        # 切换逻辑
        current_time = time.ticks_ms()
        should_switch = False
        switch_reason = ""
        
        if distance <= tracking_deadzone:
            if target_stay_time == 0:
                target_stay_time = current_time
            elif time.ticks_diff(current_time, target_stay_time) >= 500:  # 停留0.5秒后切换
                should_switch = True
                switch_reason = "到达目标"
        else:
            target_stay_time = 0
            
        # 强制切换 (防止卡住)
        if target_stay_time > 0 and time.ticks_diff(current_time, target_stay_time) >= max_stay_time:
            should_switch = True
            switch_reason = "强制切换"
        
        # 执行切换
        if should_switch:
            old_index = current_target_index
            current_target_index = (current_target_index + 1) % len(circle_trajectory)
            target_stay_time = 0
            
            print("✓ {} - 点{} → 点{}".format(switch_reason, old_index+1, current_target_index+1))
            
            # 更新目标点
            target_x, target_y = circle_trajectory[current_target_index]
            error_x = target_x - laser_x
            error_y = target_y - laser_y
            distance = math.sqrt(error_x*error_x + error_y*error_y)
        
        # 显示切换状态
        if distance <= tracking_deadzone:
            img.draw_string(10, 30, "NEAR TARGET", color=(0, 255, 255))
        else:
            img.draw_string(10, 30, "MOVING", color=(255, 255, 0))
        
        # 发送控制指令
        control_command, step_level = get_control_command((target_x, target_y), laser_pos, tracking_deadzone)
        
        if control_command:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_control_time) >= control_delay:
                direction_map = {"1": "RIGHT", "2": "LEFT", "3": "DOWN", "4": "UP"}
                direction_text = direction_map.get(control_command, "UNKNOWN")
                
                img.draw_string(10, 50, "CMD: {} L{}".format(direction_text, step_level), color=(255, 255, 0))
                
                command_data = "{},{}".format(control_command, step_level)
                uart.write(command_data + "\r\n")
                last_control_time = current_time
                
                print("发送指令: {} ({})".format(command_data, direction_text))
        else:
            img.draw_string(10, 50, "ALIGNED", color=(0, 255, 0))
    
    # 显示帧信息
    img.draw_string(10, 220, "Frame: {}".format(frame_count), color=(255, 255, 255))
    
    time.sleep_ms(100)
