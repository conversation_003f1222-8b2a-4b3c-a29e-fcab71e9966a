# 检测调试脚本 - 专门诊断识别问题

import sensor, image, time

# 初始化传感器
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

def test_black_detection(img):
    """测试黑色区域检测"""
    print("\n=== 黑色区域检测测试 ===")
    
    # 测试多种黑色阈值
    thresholds = [
        ("严格", (0, 30, -128, 127, -128, 127)),
        ("中等", (0, 50, -128, 127, -128, 127)),
        ("宽松", (0, 80, -128, 127, -128, 127)),
        ("超宽松", (0, 100, -128, 127, -128, 127))
    ]
    
    for name, threshold in thresholds:
        blobs = img.find_blobs([threshold], 
                              pixels_threshold=100, 
                              area_threshold=100,
                              merge=True)
        
        print("{} 阈值: 检测到 {} 个区域".format(name, len(blobs)))
        
        for i, blob in enumerate(blobs):
            area = blob.pixels()
            print("  区域{}: 面积={} 位置=({},{}) 尺寸={}x{}".format(
                i+1, area, blob.cx(), blob.cy(), blob.w(), blob.h()))
            
            # 绘制检测结果
            if name == "中等":  # 只绘制中等阈值的结果
                color = (0, 255, 0) if 300 <= area <= 50000 else (255, 0, 0)
                img.draw_rectangle(blob.rect(), color=color, thickness=2)
                img.draw_string(blob.cx()-10, blob.cy()-10, str(i+1), color=color)

def test_purple_detection(img):
    """测试紫色激光检测"""
    print("\n=== 紫色激光检测测试 ===")
    
    # 测试多种紫色阈值
    thresholds = [
        ("原始", (87, 100, -3, 89, -26, 0)),
        ("宽松", (20, 100, 10, 127, -128, -10)),
        ("超宽松", (10, 100, -20, 127, -128, 20))
    ]
    
    for name, threshold in thresholds:
        blobs = img.find_blobs([threshold], 
                              pixels_threshold=5, 
                              area_threshold=5,
                              merge=True)
        
        print("{} 阈值: 检测到 {} 个紫色区域".format(name, len(blobs)))
        
        for i, blob in enumerate(blobs):
            area = blob.pixels()
            print("  激光{}: 面积={} 位置=({},{})".format(
                i+1, area, blob.cx(), blob.cy()))
            
            # 绘制检测结果
            if name == "宽松" and 5 <= area <= 1000:  # 只绘制合理的激光点
                img.draw_circle(blob.cx(), blob.cy(), 8, color=(255, 0, 255), thickness=2)
                img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 255), size=15)

def test_color_analysis(img):
    """分析图像中心区域的颜色"""
    print("\n=== 颜色分析 ===")
    
    # 分析图像中心100x100区域的颜色
    center_x, center_y = 160, 120  # 图像中心
    sample_size = 50
    
    # 提取中心区域
    roi = (center_x - sample_size//2, center_y - sample_size//2, sample_size, sample_size)
    
    # 转换为LAB并分析
    lab_img = img.to_lab()
    
    # 采样几个点的LAB值
    sample_points = [
        (center_x, center_y),
        (center_x - 20, center_y),
        (center_x + 20, center_y),
        (center_x, center_y - 20),
        (center_x, center_y + 20)
    ]
    
    print("中心区域LAB颜色采样:")
    for i, (x, y) in enumerate(sample_points):
        if 0 <= x < 320 and 0 <= y < 240:
            l, a, b = lab_img.get_pixel(x, y)
            print("  点{} ({},{}): L={} A={} B={}".format(i+1, x, y, l, a, b))
            
            # 绘制采样点
            img.draw_circle(x, y, 3, color=(255, 255, 0), thickness=1)

print("检测调试脚本启动")
frame_count = 0

while True:
    img = sensor.snapshot()
    frame_count += 1
    
    # 每10帧进行一次详细检测
    if frame_count % 10 == 0:
        print("\n" + "="*60)
        print("帧 {} - 详细检测分析".format(frame_count))
        
        # 1. 黑色区域检测测试
        test_black_detection(img)
        
        # 2. 紫色激光检测测试
        test_purple_detection(img)
        
        # 3. 颜色分析
        test_color_analysis(img)
        
        print("="*60)
    
    # 显示帧信息
    img.draw_string(10, 10, "Frame: {}".format(frame_count), color=(255, 255, 255))
    img.draw_string(10, 30, "Debug Mode", color=(255, 255, 0))
    
    # 绘制中心十字线
    img.draw_line(160, 0, 160, 240, color=(128, 128, 128), thickness=1)
    img.draw_line(0, 120, 320, 120, color=(128, 128, 128), thickness=1)
    
    time.sleep_ms(200)
