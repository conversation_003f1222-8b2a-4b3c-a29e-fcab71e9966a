# OpenMV H7 Plus 激光画圆控制系统
# 功能：检测黑色矩形目标，控制紫色激光沿矩形内圆形轨迹移动
# 硬件：OpenMV H7 Plus + STM32F407 + 步进电机云台
# 通信：UART3 (P4-TX, P5-RX) 115200波特率

import sensor, image, time, math
from pyb import UART

# --------------------------- 全局配置参数 ---------------------------
# 图像传感器配置
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

# 串口配置 (UART3: P4-TX, P5-RX)
uart = UART(3, 115200)

# 画圆控制参数
circle_radius_ratio = 0.3   # 圆半径占矩形短边的比例
circle_points = 16          # 圆周分割点数
current_target_index = 0    # 当前目标点索引
circle_trajectory = []      # 圆形轨迹点列表
control_delay = 200         # 控制间隔200ms
last_control_time = 0       # 上次控制时间
tracking_deadzone = 5       # 到达目标点的死区范围

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self):
        # 紫色激光的LAB阈值 (需要根据实际情况调整)
        self.purple_threshold = (87, 100, -3, 89, -26, 0)
        self.min_area = 10      # 最小面积
        self.max_area = 700     # 最大面积

    def detect(self, img):
        """检测最亮的紫色激光点"""
        laser_points = []
        
        # 使用find_blobs检测紫色区域
        purple_blobs = img.find_blobs([self.purple_threshold],
                                    pixels_threshold=self.min_area,
                                    area_threshold=self.min_area,
                                    merge=True)
        
        # 筛选有效的激光点
        valid_blobs = []
        for blob in purple_blobs:
            if self.min_area <= blob.pixels() <= self.max_area:
                valid_blobs.append(blob)

        # 只保留最亮的一个激光点 (面积最大的通常是最亮的)
        if valid_blobs:
            brightest_blob = max(valid_blobs, key=lambda b: b.pixels())
            cx, cy = brightest_blob.cx(), brightest_blob.cy()
            laser_points.append((cx, cy))

            # 绘制检测结果
            img.draw_circle(cx, cy, 8, color=(255, 0, 255), thickness=2)
            img.draw_string(cx-25, cy-20, "Laser", color=(255, 0, 255))
            img.draw_cross(cx, cy, color=(255, 0, 255), size=15)

            print("激光点: 位置({},{}) 面积={}".format(cx, cy, brightest_blob.pixels()))

        return laser_points

# --------------------------- 圆形轨迹生成函数 ---------------------------
def generate_circle_trajectory(center, radius, num_points):
    """生成圆形轨迹点"""
    trajectory = []
    cx, cy = center
    
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        trajectory.append((x, y))
    
    return trajectory

# --------------------------- 激光圆形轨迹控制函数 ---------------------------
def get_circle_control_command(current_pos, target_pos, deadzone=5):
    """
    计算激光移动到圆形轨迹目标点的控制指令
    返回: (控制指令, 步进级别) 或 (None, 0)
    """
    if not current_pos or not target_pos:
        return None, 0

    current_x, current_y = current_pos
    target_x, target_y = target_pos

    error_x = target_x - current_x
    error_y = target_y - current_y

    # 计算总误差距离
    distance = math.sqrt(error_x*error_x + error_y*error_y)
    
    # 如果已经到达目标点附近，返回None
    if distance <= deadzone:
        return None, 0

    # 根据距离选择步进级别
    def get_step_level(dist):
        if dist > 30:       # 远距离
            return 4        # 大步进
        elif dist > 20:     # 中距离
            return 3        # 中步进
        elif dist > 10:     # 近距离
            return 2        # 小步进
        else:               # 很近
            return 1        # 微步进

    # 优先处理X轴误差，再处理Y轴误差
    if abs(error_x) > abs(error_y):
        # X轴误差更大，优先处理X轴
        direction = "2" if error_x > 0 else "1"  # 左移 或 右移
        level = get_step_level(abs(error_x))
        return direction, level
    else:
        # Y轴误差更大，优先处理Y轴
        direction = "3" if error_y > 0 else "4"  # 下移 或 上移
        level = get_step_level(abs(error_y))
        return direction, level

# --------------------------- 简化的目标区域检测函数 ---------------------------
def detect_rectangle_frame(img):
    """专门检测矩形边框 - 使用边缘检测"""
    target_areas = []

    try:
        # 转换为灰度图
        gray = img.to_grayscale()

        # 边缘检测
        edges = gray.find_edges(image.EDGE_CANNY, threshold=(50, 80))

        # 查找矩形
        rectangles = edges.find_rects(threshold=1000)

        print("边缘检测: 找到 {} 个矩形".format(len(rectangles)))

        for i, rect in enumerate(rectangles):
            area = rect.w() * rect.h()
            cx = rect.x() + rect.w() // 2
            cy = rect.y() + rect.h() // 2

            print("矩形{}: 面积={} 位置=({},{}) 尺寸={}x{}".format(
                i+1, area, cx, cy, rect.w(), rect.h()))

            # 面积过滤 - 排除太小和太大的
            if 1000 <= area <= 50000:
                # 长宽比过滤
                aspect_ratio = max(rect.w(), rect.h()) / max(min(rect.w(), rect.h()), 1)
                if aspect_ratio <= 3.0:  # 长宽比不超过3:1

                    # 创建伪blob对象
                    class RectBlob:
                        def __init__(self, rect):
                            self._rect = rect
                            self._area = rect.w() * rect.h()

                        def cx(self): return self._rect.x() + self._rect.w() // 2
                        def cy(self): return self._rect.y() + self._rect.h() // 2
                        def pixels(self): return self._area
                        def w(self): return self._rect.w()
                        def h(self): return self._rect.h()
                        def rect(self): return self._rect
                        def compactness(self): return 0.5

                    rect_blob = RectBlob(rect)
                    target_areas.append(rect_blob)

                    # 绘制检测结果
                    img.draw_rectangle(rect, color=(0, 255, 0), thickness=2)
                    img.draw_cross(cx, cy, color=(0, 255, 0), size=10)

                    print("✓ 矩形边框: 中心({},{}) 面积={} 长宽比={:.1f}".format(
                        cx, cy, area, aspect_ratio))
                else:
                    print("✗ 长宽比不符: {:.1f}".format(aspect_ratio))
            else:
                print("✗ 面积不符: {}".format(area))

    except Exception as e:
        print("边缘检测失败:", e)

    return target_areas

def detect_target_areas(img):
    """检测黑色矩形目标区域 - 优先使用边缘检测"""
    target_areas = []

    # 优先使用边缘检测
    print("尝试边缘检测...")
    target_areas = detect_rectangle_frame(img)

    if target_areas:
        print("边缘检测成功，找到 {} 个矩形".format(len(target_areas)))
        return target_areas

    print("边缘检测失败，尝试LAB检测...")
    target_areas = detect_by_lab_color(img)
    return target_areas

def detect_by_lab_color(img):
    """LAB色彩空间检测 - 作为备用方案"""
    target_areas = []

    # 多种黑色阈值尝试
    black_thresholds = [
        (0, 50, -128, 127, -128, 127),    # 原始阈值
        (0, 60, -128, 127, -128, 127),    # 稍微放宽
        (0, 80, -128, 127, -128, 127),    # 更宽范围
        (0, 100, -128, 127, -128, 127),   # 最宽范围
    ]

    all_blobs = []

    # 尝试多种阈值
    for i, threshold in enumerate(black_thresholds):
        blobs = img.find_blobs([threshold],
                              pixels_threshold=100,
                              area_threshold=100,
                              merge=True)

        print("阈值{}: 检测到 {} 个区域".format(i+1, len(blobs)))
        all_blobs.extend(blobs)

    # 去重并过滤
    unique_blobs = []
    for blob in all_blobs:
        # 检查是否与已有blob重叠
        is_duplicate = False
        for existing in unique_blobs:
            if abs(blob.cx() - existing.cx()) < 20 and abs(blob.cy() - existing.cy()) < 20:
                is_duplicate = True
                break

        if not is_duplicate:
            unique_blobs.append(blob)

    print("去重后: {} 个唯一区域".format(len(unique_blobs)))

    # 过滤和验证
    for blob in unique_blobs:
        print("区域: 面积={} 紧密度={:.2f} 位置=({},{}) 尺寸={}x{}".format(
            blob.pixels(), blob.compactness(), blob.cx(), blob.cy(), blob.w(), blob.h()))

        # 严格面积过滤 - 排除整个图像背景 (76800像素)
        if 500 <= blob.pixels() <= 20000 and blob.pixels() < 60000:
            # 非常宽松的形状过滤
            if 0.05 <= blob.compactness() <= 1.0:
                # 检查长宽比是否合理 (避免过于细长的形状)
                aspect_ratio = max(blob.w(), blob.h()) / max(min(blob.w(), blob.h()), 1)
                if aspect_ratio <= 5.0:  # 长宽比不超过5:1
                    target_areas.append(blob)

                    # 绘制检测结果
                    img.draw_rectangle(blob.rect(), color=(0, 255, 0), thickness=2)
                    img.draw_cross(blob.cx(), blob.cy(), color=(0, 255, 0), size=10)

                    print("✓ 目标矩形: 中心({},{}) 面积={} 长宽比={:.1f}".format(
                        blob.cx(), blob.cy(), blob.pixels(), aspect_ratio))
                else:
                    print("✗ 长宽比不符: {:.1f}".format(aspect_ratio))
            else:
                print("✗ 形状不符: 紧密度={:.2f}".format(blob.compactness()))
        else:
            print("✗ 面积不符: {}".format(blob.pixels()))

    # 如果LAB检测失败，尝试边缘检测
    if not target_areas:
        print("LAB检测失败，尝试边缘检测...")
        target_areas = detect_by_edges(img)

    return target_areas

def detect_by_edges(img):
    """备用检测方法：基于边缘检测"""
    target_areas = []

    try:
        # 转换为灰度图
        gray = img.to_grayscale()

        # 边缘检测
        edges = gray.find_edges(image.EDGE_CANNY, threshold=(50, 80))

        # 查找轮廓
        rectangles = edges.find_rects(threshold=1000)

        print("边缘检测: 找到 {} 个矩形".format(len(rectangles)))

        for rect in rectangles:
            # 计算矩形面积
            area = rect.w() * rect.h()

            print("矩形: 位置=({},{}) 尺寸={}x{} 面积={}".format(
                rect.x(), rect.y(), rect.w(), rect.h(), area))

            # 面积过滤
            if 500 <= area <= 20000:
                # 创建一个伪blob对象用于兼容
                class FakeBlob:
                    def __init__(self, rect):
                        self._rect = rect
                        self._area = rect.w() * rect.h()

                    def cx(self):
                        return self._rect.x() + self._rect.w() // 2

                    def cy(self):
                        return self._rect.y() + self._rect.h() // 2

                    def pixels(self):
                        return self._area

                    def w(self):
                        return self._rect.w()

                    def h(self):
                        return self._rect.h()

                    def rect(self):
                        return self._rect

                    def compactness(self):
                        return 0.5  # 假设值

                fake_blob = FakeBlob(rect)
                target_areas.append(fake_blob)

                # 绘制检测结果
                img.draw_rectangle(rect, color=(255, 255, 0), thickness=2)
                img.draw_cross(fake_blob.cx(), fake_blob.cy(), color=(255, 255, 0), size=10)

                print("✓ 边缘矩形: 中心({},{}) 面积={}".format(
                    fake_blob.cx(), fake_blob.cy(), area))

    except Exception as e:
        print("边缘检测失败:", e)

    return target_areas

# --------------------------- 主程序初始化 ---------------------------
# 创建激光检测器
laser_detector = PurpleLaserDetector()

print("激光画圆控制系统启动")
print("圆周点数:", circle_points)
print("控制间隔:", control_delay, "ms")

# --------------------------- 主循环 ---------------------------
while True:
    # 获取图像
    img = sensor.snapshot()
    
    # 1. 检测目标矩形
    print("=== 开始检测矩形 ===")
    target_areas = detect_target_areas(img)
    print("最终检测到 {} 个目标矩形".format(len(target_areas)))
    
    # 2. 检测激光点
    laser_points = laser_detector.detect(img)
    
    # 3. 如果检测到目标矩形，生成圆形轨迹
    if target_areas:
        # 使用第一个检测到的矩形
        target_blob = target_areas[0]
        center_x, center_y = target_blob.cx(), target_blob.cy()
        
        # 计算圆半径 (基于矩形的短边)
        rect_w, rect_h = target_blob.w(), target_blob.h()
        short_side = min(rect_w, rect_h)
        circle_radius = int(short_side * circle_radius_ratio)
        
        # 生成圆形轨迹
        circle_trajectory = generate_circle_trajectory(
            (center_x, center_y), circle_radius, circle_points
        )
        
        # 绘制圆形轨迹
        for i, (x, y) in enumerate(circle_trajectory):
            color = (255, 255, 0) if i == current_target_index else (128, 128, 128)
            img.draw_circle(x, y, 3, color=color, thickness=1)
            
        # 绘制当前目标点 (高亮显示)
        if circle_trajectory:
            target_x, target_y = circle_trajectory[current_target_index]
            img.draw_circle(target_x, target_y, 8, color=(255, 255, 0), thickness=3)
            img.draw_string(target_x-20, target_y-30, "Target", color=(255, 255, 0))
        
        # 4. 激光圆形轨迹控制
        if laser_points and circle_trajectory:
            laser_x, laser_y = laser_points[0]
            target_x, target_y = circle_trajectory[current_target_index]
            
            # 计算误差
            error_x = target_x - laser_x
            error_y = target_y - laser_y
            distance = math.sqrt(error_x*error_x + error_y*error_y)
            
            # 绘制连接线
            img.draw_line(laser_x, laser_y, target_x, target_y, 
                         color=(255, 255, 0), thickness=2)
            
            # 显示状态信息
            img.draw_string(10, 10, "Target: {}/{}".format(
                current_target_index+1, len(circle_trajectory)), color=(255, 255, 255))
            img.draw_string(10, 30, "Distance: {:.1f}".format(distance), color=(255, 255, 255))
            
            # 控制逻辑
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_control_time) >= control_delay:
                
                # 检查是否到达当前目标点
                if distance <= tracking_deadzone:
                    # 到达目标点，切换到下一个点
                    current_target_index = (current_target_index + 1) % len(circle_trajectory)
                    print("到达目标点 {}, 切换到下一个点 {}".format(
                        current_target_index, (current_target_index + 1) % len(circle_trajectory)))
                else:
                    # 计算控制指令
                    control_command, step_level = get_circle_control_command(
                        (laser_x, laser_y), (target_x, target_y), tracking_deadzone
                    )
                    
                    if control_command:
                        # 发送控制指令
                        command_data = "{},{}".format(control_command, step_level)
                        uart.write(command_data + "\r\n")
                        
                        # 显示控制信息
                        direction_map = {"1": "RIGHT", "2": "LEFT", "3": "DOWN", "4": "UP"}
                        direction_text = direction_map.get(control_command, "UNKNOWN")
                        step_map = {1: "微调", 2: "小步", 3: "中步", 4: "大步"}
                        step_text = step_map.get(step_level, "未知")
                        
                        img.draw_string(10, 50, "CMD: {} L{}".format(direction_text, step_level), 
                                       color=(255, 255, 0))
                        
                        print("画圆控制: {} ({} - {}) 距离={:.1f}".format(
                            command_data, direction_text, step_text, distance))
                
                last_control_time = current_time
    else:
        # 没有检测到目标矩形
        img.draw_string(10, 10, "No Target Found", color=(255, 0, 0))
        circle_trajectory = []
        current_target_index = 0
    
    # 显示图像
    time.sleep_ms(50)  # 控制帧率
