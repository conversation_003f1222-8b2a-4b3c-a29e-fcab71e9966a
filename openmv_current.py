# OpenMV H7 Plus 激光追踪控制版本
# 功能：矩形检测 + 激光点检测 + 激光追踪控制
import sensor, image, time
from pyb import UART

# --------------------------- 紫色激光检测器类 (OpenMV版本) ---------------------------
class PurpleLaserDetector:
    def __init__(self):
        # 紫色激光的LAB阈值 (需要根据实际情况调整)
        self.purple_threshold = (87, 100, -3, 89, -26, 0)#色激光笔
        self.min_area = 10      # 最小面积
        self.max_area =700    # 最大面积

    def detect(self, img):
        """检测最亮的紫色激光点 (只返回一个最明显的)"""
        laser_points = []

        # 使用find_blobs检测紫色区域
        purple_blobs = img.find_blobs([self.purple_threshold],
                                    pixels_threshold=self.min_area,
                                    area_threshold=self.min_area,
                                    merge=True)

        # 过滤符合条件的激光点
        valid_blobs = []
        for blob in purple_blobs:
            if self.min_area <= blob.pixels() <= self.max_area:
                valid_blobs.append(blob)

        # 只保留最亮的一个激光点 (面积最大的通常是最亮的)
        if valid_blobs:
            brightest_blob = max(valid_blobs, key=lambda b: b.pixels())
            cx, cy = brightest_blob.cx(), brightest_blob.cy()
            laser_points.append((cx, cy))

            # 绘制检测结果
            img.draw_circle(cx, cy, 8, color=(255, 0, 255), thickness=2)
            img.draw_string(cx-25, cy-20, "Laser", color=(255, 0, 255))
            img.draw_cross(cx, cy, color=(255, 0, 255), size=15)

            # 调试信息
            print("最亮激光点: 位置({},{}) 面积={}".format(cx, cy, brightest_blob.pixels()))

        return laser_points

# --------------------------- 激光追踪控制函数 (闭环精确控制) ---------------------------
def get_control_command_with_level(target_pos, laser_pos, deadzone=3):
    """
    根据目标位置和激光位置计算分级控制指令
    返回: (控制指令, 步进级别) 或 (None, 0)

    控制指令: "1"右/"2"左/"3"下/"4"上 (已修正方向逻辑)
    步进级别: 1=微调(0.3), 2=小步(0.6), 3=中步(0.9), 4=大步(1.2)

    方向逻辑说明 (最终修正版):
    - error_x > 0: 目标在激光右边 → 发送"2"(左移激光)
    - error_x < 0: 目标在激光左边 → 发送"1"(右移激光)
    - error_y > 0: 目标在激光下边 → 发送"3"(下移激光)
    - error_y < 0: 目标在激光上边 → 发送"4"(上移激光)
    """
    if not target_pos or not laser_pos:
        return None, 0

    target_x, target_y = target_pos
    laser_x, laser_y = laser_pos

    error_x = target_x - laser_x
    error_y = target_y - laser_y

    # 计算误差等级和对应的步进级别
    def get_step_level(error):
        abs_error = abs(error)
        if abs_error > 20:      # 大误差 > 20像素
            return 4            # 大步进 1.2
        elif abs_error > 10:    # 中误差 10-20像素
            return 3            # 中步进 0.9
        elif abs_error > 5:     # 小误差 5-10像素
            return 2            # 小步进 0.6
        else:                   # 微误差 3-5像素
            return 1            # 微步进 0.3

    # 优先处理X轴误差，再处理Y轴误差
    if abs(error_x) > deadzone:
        # 修正X轴方向逻辑：激光在右边(error_x<0)时，应该让激光往左移
        # 但如果实际效果相反，说明需要反向控制
        direction = "2" if error_x > 0 else "1"  # 最终修正：左移 或 右移
        level = get_step_level(error_x)
        return direction, level
    elif abs(error_y) > deadzone:
        # 修正Y轴方向逻辑：激光在上边(error_y<0)时，应该让激光往下移
        direction = "3" if error_y > 0 else "4"  # 修正：下移 或 上移
        level = get_step_level(error_y)
        return direction, level
    else:
        return None, 0  # 在死区范围内，不需要移动

# --------------------------- 简化的目标区域检测函数 ---------------------------
def detect_target_areas(img):
    """检测目标区域 (使用色块检测替代复杂的矩形检测)"""
    target_areas = []

    # 检测黑色区域作为目标区域
    black_threshold = (0, 30, -128, 127, -128, 127)  # LAB黑色阈值

    try:
        blobs = img.find_blobs([black_threshold],
                             pixels_threshold=500,   # 进一步降低初始筛选
                             area_threshold=500,     # 降低面积阈值
                             merge=True)

        print("检测到 {} 个黑色区域".format(len(blobs)))

        # 过滤符合条件的色块 (面积 + 长宽比)
        valid_blobs = []
        for i, blob in enumerate(blobs):
            area = blob.pixels()
            width = blob.w()
            height = blob.h()
            print("区域{}: 面积={} 尺寸{}x{}".format(i+1, area, width, height))

            # 计算宽高比 (宽度/高度)
            width_height_ratio = width / height if height > 0 else 0

            # 面积筛选
            area_ok = min_rect_area < area < max_rect_area

            # 尺寸限制 (放宽限制，避免过滤正常目标)
            max_width = 300   # 放宽最大宽度限制
            max_height = 250  # 放宽最大高度限制
            size_ok = width <= max_width and height <= max_height

            # 形状筛选 (大幅放宽条件)
            # 条件1：横向矩形 (宽/高 >= 1.05)
            # 条件2：接近正方形 (0.8 <= 高/宽 <= 1.25)
            # 条件3：轻微竖向但不太过分 (高/宽 <= 1.3)
            is_horizontal = width > height and width_height_ratio >= min_aspect_ratio
            is_nearly_square = 0.8 <= (height/width) <= 1.25 if width > 0 else False
            is_mild_vertical = height > width and (height/width) <= 1.3 if width > 0 else False

            shape_ok = is_horizontal or is_nearly_square or is_mild_vertical

            if area_ok and size_ok and shape_ok:
                valid_blobs.append(blob)
                if is_horizontal:
                    shape_type = "横向矩形"
                elif is_nearly_square:
                    shape_type = "接近正方形"
                else:
                    shape_type = "轻微竖向"
                print("符合条件的{}: 面积={} 尺寸{}x{} 宽高比={:.2f}".format(
                    shape_type, area, width, height, width_height_ratio))
            else:
                reason = []
                if not area_ok:
                    reason.append("面积{}超出范围{}-{}".format(area, min_rect_area, max_rect_area))
                if not size_ok:
                    reason.append("尺寸过大({}x{}>{}x{})".format(width, height, max_width, max_height))
                if not shape_ok:
                    height_width_ratio = height/width if width > 0 else 0
                    if height > width * 1.3:  # 过于竖向的矩形
                        reason.append("过于竖向(高/宽={:.2f}>1.3)".format(height_width_ratio))
                    else:
                        reason.append("形状不符合要求")
                print("过滤掉的区域: {} ({}x{})".format(" ".join(reason), width, height))

        # 智能选择最合适的区域 (优先选择中等大小的)
        if valid_blobs:
            # 计算理想面积 (中等大小)
            ideal_area = (min_rect_area + max_rect_area) // 2

            # 按照接近理想面积的程度排序，然后选择最接近的
            def area_score(blob):
                area = blob.pixels()
                # 距离理想面积越近，分数越高
                distance = abs(area - ideal_area)
                return -distance  # 负值，距离越小分数越高

            largest_blob = max(valid_blobs, key=area_score)

            # 创建一个简单的区域对象
            class TargetArea:
                def __init__(self, blob):
                    self.blob = blob
                def x(self): return self.blob.x()
                def y(self): return self.blob.y()
                def w(self): return self.blob.w()
                def h(self): return self.blob.h()
                def rect(self): return self.blob.rect()
                def cx(self): return self.blob.cx()
                def cy(self): return self.blob.cy()

            area = TargetArea(largest_blob)
            target_areas.append(area)

            # 绘制检测到的最大区域
            img.draw_rectangle(largest_blob.rect(), color=(0, 255, 0), thickness=2)
            img.draw_cross(largest_blob.cx(), largest_blob.cy(), color=(0, 255, 0), size=10)

            print("最大目标区域: 位置({},{}) 尺寸{}x{} 面积={}".format(
                largest_blob.x(), largest_blob.y(), largest_blob.w(), largest_blob.h(), largest_blob.pixels()))

    except Exception as e:
        print("目标检测错误:", e)

    return target_areas

# --------------------------- 主程序 (OpenMV H7 Plus版本) ---------------------------
# 摄像头初始化
sensor.reset()
sensor.set_pixformat(sensor.RGB565)    # RGB565格式
sensor.set_framesize(sensor.QVGA)      # 320x240分辨率
sensor.skip_frames(time=2000)          # 等待摄像头稳定
sensor.set_auto_gain(False)            # 关闭自动增益
sensor.set_auto_whitebal(False)        # 关闭自动白平衡

# 初始化激光检测器
laser_detector = PurpleLaserDetector()

# 初始化串口 (OpenMV使用pyb.UART)
uart = UART(3, 115200)  # 使用UART3，波特率115200
print("串口初始化完成")

# 核心参数 (激光追踪控制)
min_rect_area = 800      # 大幅降低最小面积，适应小目标
max_rect_area = 15000    # 适中的最大面积
min_aspect_ratio = 1.0   # 放宽宽高比，接受正方形

# 激光追踪控制参数 (闭环精确控制)
tracking_deadzone = 3    # 缩小死区，提高精度
control_delay = 150      # 增加控制间隔，给步进电机更多时间

# 分级控制参数 (根据误差大小调整步进量)
large_error_threshold = 20   # 大误差阈值
medium_error_threshold = 10  # 中误差阈值
small_error_threshold = 5    # 小误差阈值

# 控制频率限制
last_control_time = 0    # 上次发送控制指令的时间

print("OpenMV H7 Plus 第一阶段启动完成")

# 主循环
while True:
    # 读取图像
    img = sensor.snapshot()

    # 1. 检测最大的目标区域
    target_areas = detect_target_areas(img)

    # 2. 获取目标中心点 (激光追踪目标)
    target_center = None
    if target_areas:  # 确保有检测到目标
        area = target_areas[0]  # 只取第一个(也是唯一的最大区域)

        # 获取目标中心点
        target_center = (area.cx(), area.cy())
        center_x, center_y = target_center

        # 绘制目标中心点 (绿色圆圈)
        img.draw_circle(center_x, center_y, 12, color=(0, 255, 0), thickness=2)  # 绿色中心点
        img.draw_cross(center_x, center_y, color=(0, 255, 0), size=20)  # 绿色十字
        img.draw_string(center_x-30, center_y-30, "TARGET", color=(0, 255, 0))

        print("目标中心: ({},{})".format(center_x, center_y))
    else:
        print("未检测到目标区域")

    # 3. 激光检测
    laser_points = laser_detector.detect(img)

    # 4. 激光追踪控制逻辑
    if target_center and laser_points:
        laser_pos = laser_points[0]  # 取第一个(最亮的)激光点
        laser_x, laser_y = laser_pos
        target_x, target_y = target_center

        # 计算位置误差
        error_x = target_x - laser_x
        error_y = target_y - laser_y

        # 绘制追踪状态可视化
        img.draw_line(laser_x, laser_y, target_x, target_y, color=(255, 255, 0), thickness=2)  # 黄色连接线
        img.draw_string(10, 10, "Error: X={} Y={}".format(error_x, error_y), color=(255, 255, 255))

        # 计算分级控制指令
        control_command, step_level = get_control_command_with_level(target_center, laser_pos, tracking_deadzone)

        if control_command:
            # 控制频率限制
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_control_time) >= control_delay:
                # 显示控制方向和步进级别 (已修正方向逻辑)
                direction_map = {"1": "RIGHT", "2": "LEFT", "3": "DOWN", "4": "UP"}
                direction_text = direction_map.get(control_command, "UNKNOWN")
                step_map = {1: "微调", 2: "小步", 3: "中步", 4: "大步"}
                step_text = step_map.get(step_level, "未知")

                img.draw_string(10, 30, "CMD: {} L{}".format(direction_text, step_level), color=(255, 255, 0))
                img.draw_string(10, 50, "STEP: {}".format(step_text), color=(255, 255, 0))

                # 发送分级控制指令 (格式: "方向,级别")
                command_data = "{},{}".format(control_command, step_level)
                uart.write(command_data + "\r\n")
                last_control_time = current_time

                print("发送分级控制指令: {} ({} - {})".format(command_data, direction_text, step_text))
                print("  激光位置: ({},{}) -> 目标位置: ({},{})".format(laser_x, laser_y, target_x, target_y))
                print("  误差: X={} Y={} -> 步进级别: {}".format(error_x, error_y, step_level))
            else:
                img.draw_string(10, 30, "WAIT...", color=(255, 128, 0))
        else:
            img.draw_string(10, 30, "ALIGNED!", color=(0, 255, 0))
            print("激光已对准目标! 误差在死区范围内 (±{})".format(tracking_deadzone))

    elif target_center and not laser_points:
        print("检测到目标但未检测到激光点")
        img.draw_string(10, 30, "NO LASER DETECTED", color=(255, 0, 0))

    elif not target_center and laser_points:
        print("检测到激光但未检测到目标")
        img.draw_string(10, 30, "NO TARGET DETECTED", color=(255, 0, 0))

    else:
        print("未检测到目标和激光点")
        img.draw_string(10, 30, "NO TARGET & LASER", color=(255, 0, 0))

    # 延时
    time.sleep_ms(50)
