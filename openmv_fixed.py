# OpenMV H7 Plus 激光追踪控制版本 - 修复检测问题
# 功能：矩形检测 + 激光点检测 + 激光追踪控制
import sensor, image, time
from pyb import UART

# --------------------------- 紫色激光检测器类 (修复版本) ---------------------------
class PurpleLaserDetector:
    def __init__(self):
        # 紫色激光的LAB阈值 (放宽检测范围)
        self.purple_threshold = (20, 100, 10, 127, -128, -10)  # 更宽泛的紫色范围
        self.min_area = 5       # 降低最小面积
        self.max_area = 1000    # 增加最大面积

    def detect(self, img):
        """检测最亮的紫色激光点 (只返回一个最明显的)"""
        laser_points = []

        try:
            # 使用find_blobs检测紫色区域
            purple_blobs = img.find_blobs([self.purple_threshold],
                                        pixels_threshold=self.min_area,
                                        area_threshold=self.min_area,
                                        merge=True)

            print("检测到 {} 个紫色区域".format(len(purple_blobs)))

            # 过滤符合条件的激光点
            valid_blobs = []
            for i, blob in enumerate(purple_blobs):
                area = blob.pixels()
                print("紫色区域{}: 面积={} 位置({},{})".format(i+1, area, blob.cx(), blob.cy()))
                
                if self.min_area <= area <= self.max_area:
                    valid_blobs.append(blob)
                    print("  符合条件的激光点")
                else:
                    print("  面积超出范围 {}-{}".format(self.min_area, self.max_area))

            # 只保留最亮的一个激光点 (面积最大的通常是最亮的)
            if valid_blobs:
                brightest_blob = max(valid_blobs, key=lambda b: b.pixels())
                cx, cy = brightest_blob.cx(), brightest_blob.cy()
                laser_points.append((cx, cy))

                # 绘制检测结果
                img.draw_circle(cx, cy, 8, color=(255, 0, 255), thickness=2)
                img.draw_string(cx-25, cy-20, "Laser", color=(255, 0, 255))
                img.draw_cross(cx, cy, color=(255, 0, 255), size=15)

                # 调试信息
                print("✓ 最亮激光点: 位置({},{}) 面积={}".format(cx, cy, brightest_blob.pixels()))
            else:
                print("✗ 未找到符合条件的激光点")

        except Exception as e:
            print("激光检测错误:", e)

        return laser_points

# --------------------------- 简化的目标区域检测函数 (修复版本) ---------------------------
def detect_target_areas(img):
    """检测目标区域 - 修复版本"""
    target_areas = []

    # 检测黑色区域作为目标区域 (放宽阈值)
    black_threshold = (0, 40, -128, 127, -128, 127)  # 放宽黑色检测范围

    try:
        blobs = img.find_blobs([black_threshold],
                             pixels_threshold=200,   # 大幅降低初始筛选
                             area_threshold=200,     # 大幅降低面积阈值
                             merge=True)

        print("检测到 {} 个黑色区域".format(len(blobs)))

        # 过滤符合条件的色块 (简化条件)
        valid_blobs = []
        for i, blob in enumerate(blobs):
            area = blob.pixels()
            width = blob.w()
            height = blob.h()
            print("黑色区域{}: 面积={} 尺寸{}x{}".format(i+1, area, width, height))

            # 简化的面积筛选 (大幅放宽)
            area_ok = 500 <= area <= 20000  # 放宽面积范围

            # 简化的尺寸限制
            size_ok = width >= 20 and height >= 20 and width <= 400 and height <= 300

            # 简化的形状筛选 (接受更多形状)
            if width > 0 and height > 0:
                aspect_ratio = max(width, height) / min(width, height)
                shape_ok = aspect_ratio <= 3.0  # 宽高比不超过3:1
            else:
                shape_ok = False

            if area_ok and size_ok and shape_ok:
                valid_blobs.append(blob)
                print("  ✓ 符合条件: 面积={} 尺寸{}x{} 宽高比={:.2f}".format(
                    area, width, height, max(width, height) / min(width, height)))
            else:
                reason = []
                if not area_ok:
                    reason.append("面积{}超出范围500-20000".format(area))
                if not size_ok:
                    reason.append("尺寸不符合要求({}x{})".format(width, height))
                if not shape_ok:
                    reason.append("形状比例不合适")
                print("  ✗ 过滤: {}".format(" ".join(reason)))

        # 选择最大的区域
        if valid_blobs:
            largest_blob = max(valid_blobs, key=lambda b: b.pixels())

            # 创建一个简单的区域对象
            class TargetArea:
                def __init__(self, blob):
                    self.blob = blob
                def x(self): return self.blob.x()
                def y(self): return self.blob.y()
                def w(self): return self.blob.w()
                def h(self): return self.blob.h()
                def rect(self): return self.blob.rect()
                def cx(self): return self.blob.cx()
                def cy(self): return self.blob.cy()

            area = TargetArea(largest_blob)
            target_areas.append(area)

            # 绘制检测到的最大区域
            img.draw_rectangle(largest_blob.rect(), color=(0, 255, 0), thickness=2)
            img.draw_cross(largest_blob.cx(), largest_blob.cy(), color=(0, 255, 0), size=10)

            print("✓ 最大目标区域: 位置({},{}) 尺寸{}x{} 面积={}".format(
                largest_blob.x(), largest_blob.y(), largest_blob.w(), largest_blob.h(), largest_blob.pixels()))
        else:
            print("✗ 未找到符合条件的目标区域")

    except Exception as e:
        print("目标检测错误:", e)

    return target_areas

# --------------------------- 激光追踪控制函数 ---------------------------
def get_control_command_with_level(target_pos, laser_pos, deadzone=3):
    """根据目标位置和激光位置计算分级控制指令"""
    if not target_pos or not laser_pos:
        return None, 0

    target_x, target_y = target_pos
    laser_x, laser_y = laser_pos

    error_x = target_x - laser_x
    error_y = target_y - laser_y

    # 计算误差等级和对应的步进级别
    def get_step_level(error):
        abs_error = abs(error)
        if abs_error > 20:      # 大误差 > 20像素
            return 4            # 大步进 1.2
        elif abs_error > 10:    # 中误差 10-20像素
            return 3            # 中步进 0.9
        elif abs_error > 5:     # 小误差 5-10像素
            return 2            # 小步进 0.6
        else:                   # 微误差 3-5像素
            return 1            # 微步进 0.3

    # 优先处理X轴误差，再处理Y轴误差
    if abs(error_x) > deadzone:
        direction = "2" if error_x > 0 else "1"  # 左移 或 右移
        level = get_step_level(error_x)
        return direction, level
    elif abs(error_y) > deadzone:
        direction = "3" if error_y > 0 else "4"  # 下移 或 上移
        level = get_step_level(error_y)
        return direction, level
    else:
        return None, 0  # 在死区范围内，不需要移动

# --------------------------- 主程序 ---------------------------
# 摄像头初始化
sensor.reset()
sensor.set_pixformat(sensor.RGB565)    # RGB565格式
sensor.set_framesize(sensor.QVGA)      # 320x240分辨率
sensor.skip_frames(time=2000)          # 等待摄像头稳定
sensor.set_auto_gain(False)            # 关闭自动增益
sensor.set_auto_whitebal(False)        # 关闭自动白平衡

# 初始化激光检测器
laser_detector = PurpleLaserDetector()

# 初始化串口
uart = UART(3, 115200)
print("串口初始化完成")

# 控制参数
tracking_deadzone = 5    # 增大死区
control_delay = 200      # 控制间隔
last_control_time = 0

print("OpenMV H7 Plus 启动完成 - 修复版本")
print("检测参数:")
print("- 紫色激光阈值: (20, 100, 10, 127, -128, -10)")
print("- 黑色区域阈值: (0, 40, -128, 127, -128, 127)")
print("- 激光面积范围: 5-1000")
print("- 目标面积范围: 500-20000")

frame_count = 0

# 主循环
while True:
    # 读取图像
    img = sensor.snapshot()
    frame_count += 1

    # 显示帧计数
    img.draw_string(10, 220, "Frame: {}".format(frame_count), color=(255, 255, 255))

    # 1. 检测目标区域
    target_areas = detect_target_areas(img)

    # 2. 获取目标中心点
    target_center = None
    if target_areas:
        area = target_areas[0]
        target_center = (area.cx(), area.cy())
        center_x, center_y = target_center

        # 绘制目标中心点
        img.draw_circle(center_x, center_y, 12, color=(0, 255, 0), thickness=2)
        img.draw_cross(center_x, center_y, color=(0, 255, 0), size=20)
        img.draw_string(center_x-30, center_y-30, "TARGET", color=(0, 255, 0))

        print("目标中心: ({},{})".format(center_x, center_y))

    # 3. 激光检测
    laser_points = laser_detector.detect(img)

    # 4. 控制逻辑
    if target_center and laser_points:
        laser_pos = laser_points[0]
        laser_x, laser_y = laser_pos
        target_x, target_y = target_center

        # 计算位置误差
        error_x = target_x - laser_x
        error_y = target_y - laser_y

        # 绘制连接线
        img.draw_line(laser_x, laser_y, target_x, target_y, color=(255, 255, 0), thickness=2)
        img.draw_string(10, 10, "Error: X={} Y={}".format(error_x, error_y), color=(255, 255, 255))

        # 计算控制指令
        control_command, step_level = get_control_command_with_level(target_center, laser_pos, tracking_deadzone)

        if control_command:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_control_time) >= control_delay:
                direction_map = {"1": "RIGHT", "2": "LEFT", "3": "DOWN", "4": "UP"}
                direction_text = direction_map.get(control_command, "UNKNOWN")

                img.draw_string(10, 30, "CMD: {} L{}".format(direction_text, step_level), color=(255, 255, 0))

                command_data = "{},{}".format(control_command, step_level)
                uart.write(command_data + "\r\n")
                last_control_time = current_time

                print("发送指令: {} ({})".format(command_data, direction_text))
        else:
            img.draw_string(10, 30, "ALIGNED!", color=(0, 255, 0))

    elif target_center and not laser_points:
        img.draw_string(10, 30, "NO LASER", color=(255, 0, 0))
    elif not target_center and laser_points:
        img.draw_string(10, 30, "NO TARGET", color=(255, 0, 0))
    else:
        img.draw_string(10, 30, "NO DETECTION", color=(255, 0, 0))

    # 延时
    time.sleep_ms(100)
