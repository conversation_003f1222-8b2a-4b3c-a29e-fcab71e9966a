'''
实验名称：最小矩形检测与全角度圆形校正
实验平台：OpenMV H7 Plus
说明：基于矩形主方向角精确校正透视变形，确保任意角度下圆形都显示为正圆
'''

import time, os, sys
import math
import sensor, image
from pyb import UART

# --------------------------- 卡尔曼滤波器类 ---------------------------
class KalmanFilter2D:
    """二维卡尔曼滤波器，用于跟踪矩形中心位置"""

    def __init__(self, process_noise=1.0, measurement_noise=1.0, estimation_error=1.0):
        """
        初始化卡尔曼滤波器
        process_noise: 过程噪声 (系统不确定性)
        measurement_noise: 测量噪声 (传感器不确定性)
        estimation_error: 估计误差 (初始不确定性)
        """
        # 状态向量 [x, y, vx, vy] - 位置和速度
        self.x = [0.0, 0.0, 0.0, 0.0]  # 初始状态

        # 状态协方差矩阵 P (4x4)
        self.P = [[estimation_error, 0, 0, 0],
                  [0, estimation_error, 0, 0],
                  [0, 0, estimation_error, 0],
                  [0, 0, 0, estimation_error]]

        # 过程噪声协方差矩阵 Q (4x4)
        self.Q = [[process_noise, 0, 0, 0],
                  [0, process_noise, 0, 0],
                  [0, 0, process_noise, 0],
                  [0, 0, 0, process_noise]]

        # 测量噪声协方差矩阵 R (2x2)
        self.R = [[measurement_noise, 0],
                  [0, measurement_noise]]

        # 状态转移矩阵 F (4x4) - 假设匀速运动
        self.dt = 1.0  # 时间步长
        self.F = [[1, 0, self.dt, 0],
                  [0, 1, 0, self.dt],
                  [0, 0, 1, 0],
                  [0, 0, 0, 1]]

        # 观测矩阵 H (2x4) - 只能观测位置
        self.H = [[1, 0, 0, 0],
                  [0, 1, 0, 0]]

        self.initialized = False
        self.last_update_time = 0

    def matrix_multiply(self, A, B):
        """矩阵乘法"""
        rows_A, cols_A = len(A), len(A[0])
        rows_B, cols_B = len(B), len(B[0])

        if cols_A != rows_B:
            return None

        result = [[0 for _ in range(cols_B)] for _ in range(rows_A)]

        for i in range(rows_A):
            for j in range(cols_B):
                for k in range(cols_A):
                    result[i][j] += A[i][k] * B[k][j]
        return result

    def matrix_add(self, A, B):
        """矩阵加法"""
        rows, cols = len(A), len(A[0])
        result = [[0 for _ in range(cols)] for _ in range(rows)]

        for i in range(rows):
            for j in range(cols):
                result[i][j] = A[i][j] + B[i][j]
        return result

    def matrix_subtract(self, A, B):
        """矩阵减法"""
        rows, cols = len(A), len(A[0])
        result = [[0 for _ in range(cols)] for _ in range(rows)]

        for i in range(rows):
            for j in range(cols):
                result[i][j] = A[i][j] - B[i][j]
        return result

    def matrix_transpose(self, A):
        """矩阵转置"""
        rows, cols = len(A), len(A[0])
        result = [[0 for _ in range(rows)] for _ in range(cols)]

        for i in range(rows):
            for j in range(cols):
                result[j][i] = A[i][j]
        return result

    def matrix_inverse_2x2(self, A):
        """2x2矩阵求逆"""
        det = A[0][0] * A[1][1] - A[0][1] * A[1][0]
        if abs(det) < 1e-10:
            return None

        inv_det = 1.0 / det
        return [[A[1][1] * inv_det, -A[0][1] * inv_det],
                [-A[1][0] * inv_det, A[0][0] * inv_det]]

    def predict(self, dt=None):
        """预测步骤"""
        if dt is not None:
            self.dt = dt
            # 更新状态转移矩阵
            self.F[0][2] = self.dt
            self.F[1][3] = self.dt

        # 预测状态: x = F * x
        new_x = [0.0, 0.0, 0.0, 0.0]
        for i in range(4):
            for j in range(4):
                new_x[i] += self.F[i][j] * self.x[j]
        self.x = new_x

        # 预测协方差: P = F * P * F^T + Q
        FP = self.matrix_multiply(self.F, self.P)
        FT = self.matrix_transpose(self.F)
        FPFT = self.matrix_multiply(FP, FT)
        self.P = self.matrix_add(FPFT, self.Q)

    def update(self, measurement):
        """更新步骤"""
        if not self.initialized:
            # 首次初始化
            self.x[0] = measurement[0]  # x位置
            self.x[1] = measurement[1]  # y位置
            self.x[2] = 0.0            # x速度
            self.x[3] = 0.0            # y速度
            self.initialized = True
            self.last_update_time = time.ticks_ms()
            return (self.x[0], self.x[1])

        # 计算时间间隔
        current_time = time.ticks_ms()
        dt = time.ticks_diff(current_time, self.last_update_time) / 1000.0
        self.last_update_time = current_time

        # 预测步骤
        self.predict(dt)

        # 计算创新(残差): y = z - H * x
        Hx = [self.H[0][0] * self.x[0] + self.H[0][1] * self.x[1] + self.H[0][2] * self.x[2] + self.H[0][3] * self.x[3],
              self.H[1][0] * self.x[0] + self.H[1][1] * self.x[1] + self.H[1][2] * self.x[2] + self.H[1][3] * self.x[3]]

        innovation = [measurement[0] - Hx[0], measurement[1] - Hx[1]]

        # 计算创新协方差: S = H * P * H^T + R
        HP = self.matrix_multiply(self.H, self.P)
        HT = self.matrix_transpose(self.H)
        HPHT = self.matrix_multiply(HP, HT)
        S = self.matrix_add(HPHT, self.R)

        # 计算卡尔曼增益: K = P * H^T * S^(-1)
        S_inv = self.matrix_inverse_2x2(S)
        if S_inv is None:
            return (self.x[0], self.x[1])

        PHT = self.matrix_multiply(self.P, HT)
        K = self.matrix_multiply(PHT, S_inv)

        # 更新状态: x = x + K * y
        for i in range(4):
            self.x[i] += K[i][0] * innovation[0] + K[i][1] * innovation[1]

        # 更新协方差: P = (I - K * H) * P
        KH = self.matrix_multiply(K, self.H)
        I = [[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]]
        I_KH = self.matrix_subtract(I, KH)
        self.P = self.matrix_multiply(I_KH, self.P)

        return (self.x[0], self.x[1])

    def get_predicted_position(self, dt=1.0):
        """获取预测位置(不更新状态)"""
        if not self.initialized:
            return None

        # 简单的线性预测: pos = current_pos + velocity * dt
        pred_x = self.x[0] + self.x[2] * dt
        pred_y = self.x[1] + self.x[3] * dt
        return (pred_x, pred_y)

    def get_velocity(self):
        """获取当前速度"""
        if not self.initialized:
            return (0.0, 0.0)
        return (self.x[2], self.x[3])

    def reset(self):
        """重置滤波器"""
        self.initialized = False
        self.x = [0.0, 0.0, 0.0, 0.0]

# --------------------------- 硬件初始化 ---------------------------
# 串口初始化 (OpenMV使用pyb.UART)
uart = UART(3, 115200)  # 使用UART3，波特率115200
print("串口初始化完成")

# 摄像头初始化
sensor.reset()
sensor.set_pixformat(sensor.RGB565)    # RGB565格式
sensor.set_framesize(sensor.QQVGA)     # 160x120分辨率
sensor.skip_frames(time=2000)          # 等待摄像头稳定
sensor.set_auto_gain(False)            # 关闭自动增益
sensor.set_auto_whitebal(False)        # 关闭自动白平衡

# LCD初始化已移除

# 初始化卡尔曼滤波器
kalman_filter = KalmanFilter2D(
    process_noise=0.5,      # 过程噪声 - 系统运动的不确定性
    measurement_noise=2.0,  # 测量噪声 - 检测精度的不确定性
    estimation_error=10.0   # 初始估计误差
)

# --------------------------- 配置参数 ---------------------------
thresholds = [(0, 150)]        # 二值化阈值
MIN_AREA = 2500               # 最小面积阈值 (QQVGA: 10000÷4)
MAX_AREA = 12500             # 最大面积阈值 (QQVGA: 50000÷4)
MIN_ASPECT_RATIO = 0.3        # 最小宽高比
MAX_ASPECT_RATIO = 3.0        # 最大宽高比

BASE_RADIUS = 45              # 基础半径（虚拟坐标单位）
POINTS_PER_CIRCLE = 24        # 增加采样点使圆形更平滑
PURPLE_THRESHOLD = (20, 60, 15, 70, -70, -20)  # 紫色色块阈值

# 基础矩形比例（可根据实际需求调整）
RECT_WIDTH = 160              # 虚拟矩形宽度 (QQVGA适配)
RECT_HEIGHT = 90              # 虚拟矩形高度 (QQVGA适配)
TARGET_ASPECT_RATIO = RECT_WIDTH / RECT_HEIGHT  # 目标宽高比

# --------------------------- 工具函数 ---------------------------
def calculate_distance(p1, p2):
    return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)

def calculate_center(points):
    if not points:
        return (0, 0)
    sum_x = sum(p[0] for p in points)
    sum_y = sum(p[1] for p in points)
    return (sum_x / len(points), sum_y / len(points))

def is_valid_rect(corners):
    edges = [calculate_distance(corners[i], corners[(i+1)%4]) for i in range(4)]

    ratio1 = edges[0] / max(edges[2], 0.1)
    ratio2 = edges[1] / max(edges[3], 0.1)
    valid_ratio = 0.5 < ratio1 < 1.5 and 0.5 < ratio2 < 1.5

    area = 0
    for i in range(4):
        x1, y1 = corners[i]
        x2, y2 = corners[(i+1) % 4]
        area += (x1 * y2 - x2 * y1)
    area = abs(area) / 2
    valid_area = MIN_AREA < area < MAX_AREA

    width = max(p[0] for p in corners) - min(p[0] for p in corners)
    height = max(p[1] for p in corners) - min(p[1] for p in corners)
    aspect_ratio = width / max(height, 0.1)
    valid_aspect = MIN_ASPECT_RATIO < aspect_ratio < MAX_ASPECT_RATIO

    return valid_ratio and valid_area and valid_aspect

def detect_purple_blobs(img):
    return img.find_blobs(
        [PURPLE_THRESHOLD],
        pixels_threshold=25,      # QQVGA: 100÷4
        area_threshold=25,        # QQVGA: 100÷4
        merge=True
    )

def send_circle_points(points):
    if not points:
        return
    count = len(points)
    msg = "$$C,%d," % count
    for x, y in points:
        msg += "%d,%d," % (x, y)
    msg = msg.rstrip(',') + "##"
    uart.write(msg + "\r\n")
    print("发送圆形点: %s" % msg)

def send_rect_center(center_x, center_y):
    """发送矩形中心坐标"""
    msg = "$$R,%d,%d##" % (center_x, center_y)
    uart.write(msg + "\r\n")
    print("发送矩形中心: %s" % msg)

def get_perspective_matrix(src_pts, dst_pts):
    """计算透视变换矩阵"""
    A = []
    B = []
    for i in range(4):
        x, y = src_pts[i]
        u, v = dst_pts[i]
        A.append([x, y, 1, 0, 0, 0, -u*x, -u*y])
        A.append([0, 0, 0, x, y, 1, -v*x, -v*y])
        B.append(u)
        B.append(v)

    # 高斯消元求解矩阵
    n = 8
    for i in range(n):
        max_row = i
        for j in range(i, len(A)):
            if abs(A[j][i]) > abs(A[max_row][i]):
                max_row = j
        A[i], A[max_row] = A[max_row], A[i]
        B[i], B[max_row] = B[max_row], B[i]

        pivot = A[i][i]
        if abs(pivot) < 1e-8:
            return None
        for j in range(i, n):
            A[i][j] /= pivot
        B[i] /= pivot

        for j in range(len(A)):
            if j != i and A[j][i] != 0:
                factor = A[j][i]
                for k in range(i, n):
                    A[j][k] -= factor * A[i][k]
                B[j] -= factor * B[i]

    return [
        [B[0], B[1], B[2]],
        [B[3], B[4], B[5]],
        [B[6], B[7], 1.0]
    ]

def transform_points(points, matrix):
    """应用透视变换将虚拟坐标映射到原始图像坐标"""
    transformed = []
    for (x, y) in points:
        x_hom = x * matrix[0][0] + y * matrix[0][1] + matrix[0][2]
        y_hom = x * matrix[1][0] + y * matrix[1][1] + matrix[1][2]
        w_hom = x * matrix[2][0] + y * matrix[2][1] + matrix[2][2]
        if abs(w_hom) > 1e-8:
            transformed.append((x_hom / w_hom, y_hom / w_hom))
    return transformed

def sort_corners(corners):
    """将矩形角点按左上、右上、右下、左下顺序排序"""
    center = calculate_center(corners)
    sorted_corners = sorted(corners, key=lambda p: math.atan2(p[1]-center[1], p[0]-center[0]))

    # 调整顺序为左上、右上、右下、左下
    if len(sorted_corners) == 4:
        # 找到最接近左上角的点
        left_top = min(sorted_corners, key=lambda p: p[0]+p[1])
        index = sorted_corners.index(left_top)
        sorted_corners = sorted_corners[index:] + sorted_corners[:index]
    return sorted_corners

def get_rectangle_orientation(corners):
    """计算矩形的主方向角（水平边与x轴的夹角）"""
    # 假设排序后的角点顺序为：左上、右上、右下、左下
    if len(corners) != 4:
        return 0

    # 计算上边和右边的向量
    top_edge = (corners[1][0] - corners[0][0], corners[1][1] - corners[0][1])
    right_edge = (corners[2][0] - corners[1][0], corners[2][1] - corners[1][1])

    # 选择较长的边作为主方向
    if calculate_distance(corners[0], corners[1]) > calculate_distance(corners[1], corners[2]):
        main_edge = top_edge
    else:
        main_edge = right_edge

    # 计算主方向角（弧度）
    angle = math.atan2(main_edge[1], main_edge[0])
    return angle

# 跟踪状态管理
lost_frames = 0              # 连续丢失帧数
max_lost_frames = 5          # 最大允许丢失帧数
prediction_mode = False      # 是否处于预测模式
last_detected_center = None  # 最后检测到的中心位置

print("OpenMV透视校正系统启动完成")
print("检测参数:")
print("- 二值化阈值: (0, 150)")
print("- 矩形面积范围: %d - %d" % (MIN_AREA, MAX_AREA))
print("- 圆形采样点数: %d" % POINTS_PER_CIRCLE)
print("- 卡尔曼滤波器已启用")
print("- 最大丢失帧数: %d" % max_lost_frames)

# --------------------------- 主循环 ---------------------------
clock = time.clock()
while True:
    clock.tick()
    img = sensor.snapshot()

    # 图像处理
    gray_img = img.to_grayscale()
    binary_img = gray_img.binary(thresholds)
    binary_img.erode(1)
    binary_img.dilate(3)

    # 检测紫色色块
    purple_blobs = detect_purple_blobs(img)
    for blob in purple_blobs:
        img.draw_rectangle(blob.rect(), color=(255, 0, 255), thickness=1)
        img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 255), thickness=1)

    # 检测所有矩形并筛选出最小的一个
    min_area = float('inf')
    smallest_rect = None

    for r in binary_img.find_rects(threshold=3000):  # QQVGA: 12000÷4
        corners = r.corners()
        if is_valid_rect(corners):
            # 计算矩形面积
            area = 0
            for i in range(4):
                x1, y1 = corners[i]
                x2, y2 = corners[(i+1) % 4]
                area += (x1 * y2 - x2 * y1)
            area = abs(area) / 2

            # 更新最小矩形
            if area < min_area:
                min_area = area
                smallest_rect = corners

    # 处理矩形检测结果
    detected_center = None
    if smallest_rect:
        # 对矩形角点进行排序
        sorted_corners = sort_corners(smallest_rect)

        # 计算原始检测的矩形中心点
        raw_center = calculate_center(sorted_corners)
        detected_center = (raw_center[0], raw_center[1])

        # 使用卡尔曼滤波器更新位置
        filtered_center = kalman_filter.update(detected_center)
        center_x, center_y = int(filtered_center[0]), int(filtered_center[1])

        # 重置丢失计数
        lost_frames = 0
        prediction_mode = False
        last_detected_center = detected_center

        print("检测到矩形: 原始({:.1f},{:.1f}) 滤波({:.1f},{:.1f})".format(
            detected_center[0], detected_center[1], filtered_center[0], filtered_center[1]))

    else:
        # 未检测到矩形，使用卡尔曼滤波器预测
        lost_frames += 1

        if lost_frames <= max_lost_frames and kalman_filter.initialized:
            # 使用预测位置
            predicted_center = kalman_filter.get_predicted_position(dt=1.0)
            if predicted_center:
                center_x, center_y = int(predicted_center[0]), int(predicted_center[1])
                prediction_mode = True

                # 获取速度信息
                velocity = kalman_filter.get_velocity()
                print("预测模式: 位置({:.1f},{:.1f}) 速度({:.1f},{:.1f}) 丢失帧数:{}".format(
                    predicted_center[0], predicted_center[1], velocity[0], velocity[1], lost_frames))
            else:
                center_x = center_y = None
        else:
            # 超过最大丢失帧数，重置跟踪
            if lost_frames > max_lost_frames:
                print("跟踪丢失，重置卡尔曼滤波器")
                kalman_filter.reset()
                prediction_mode = False
            center_x = center_y = None

    # 绘制检测和跟踪结果
    if center_x is not None and center_y is not None:

        # 绘制矩形边框和角点(仅在检测模式下)
        if not prediction_mode and smallest_rect:
            for i in range(4):
                x1, y1 = sorted_corners[i]
                x2, y2 = sorted_corners[(i+1) % 4]
                img.draw_line(x1, y1, x2, y2, color=(255, 0, 0), thickness=1)
            for p in sorted_corners:
                img.draw_circle(p[0], p[1], 3, color=(0, 255, 0), thickness=1)

        # 绘制中心点 - 根据模式使用不同颜色
        if prediction_mode:
            # 预测模式 - 红色标记
            img.draw_circle(center_x, center_y, 6, color=(255, 0, 0), thickness=2)    # 红色外圈
            img.draw_circle(center_x, center_y, 3, color=(255, 0, 0), thickness=-1)   # 红色实心圆
            img.draw_line(center_x-7, center_y, center_x+7, center_y, color=(255, 0, 0), thickness=1)
            img.draw_line(center_x, center_y-7, center_x, center_y+7, color=(255, 0, 0), thickness=1)

            # 显示预测状态
            img.draw_string(center_x-20, center_y-20, "PRED", color=(255, 0, 0))

            # 绘制原始检测位置(如果有)
            if detected_center:
                raw_x, raw_y = int(detected_center[0]), int(detected_center[1])
                img.draw_circle(raw_x, raw_y, 2, color=(128, 128, 128), thickness=1)  # 灰色小圆
        else:
            # 检测模式 - 黄色标记
            img.draw_circle(center_x, center_y, 6, color=(255, 255, 0), thickness=2)  # 黄色外圈
            img.draw_circle(center_x, center_y, 3, color=(255, 255, 0), thickness=-1) # 黄色实心圆
            img.draw_line(center_x-7, center_y, center_x+7, center_y, color=(255, 255, 0), thickness=1)
            img.draw_line(center_x, center_y-7, center_x, center_y+7, color=(255, 255, 0), thickness=1)

            # 显示检测状态
            img.draw_string(center_x-20, center_y-20, "DETECT", color=(255, 255, 0))

        # 发送矩形中心坐标
        send_rect_center(center_x, center_y)

        # 透视校正只在检测模式下进行
        if not prediction_mode and smallest_rect:
            # 计算矩形主方向角
            angle = get_rectangle_orientation(sorted_corners)

            # 计算矩形实际宽高
            width = calculate_distance(sorted_corners[0], sorted_corners[1])
            height = calculate_distance(sorted_corners[1], sorted_corners[2])
            actual_aspect = width / max(height, 0.1)

            # 确定矩形是横向还是纵向（考虑旋转）
            is_horizontal = actual_aspect >= 1.0

            # 构建虚拟矩形（根据方向调整）
            if is_horizontal:
                virtual_rect = [
                    (0, 0),
                    (RECT_WIDTH, 0),
                    (RECT_WIDTH, RECT_HEIGHT),
                    (0, RECT_HEIGHT)
                ]
            else:
                virtual_rect = [
                    (0, 0),
                    (RECT_HEIGHT, 0),
                    (RECT_HEIGHT, RECT_WIDTH),
                    (0, RECT_WIDTH)
                ]

            # 计算校正半径（基于实际宽高比）
            if is_horizontal:
                radius_x = BASE_RADIUS
                radius_y = BASE_RADIUS / actual_aspect
            else:
                radius_x = BASE_RADIUS * actual_aspect
                radius_y = BASE_RADIUS

            # 计算虚拟矩形中心
            virtual_center = (RECT_WIDTH/2, RECT_HEIGHT/2) if is_horizontal else (RECT_HEIGHT/2, RECT_WIDTH/2)

            # 在虚拟矩形中生成椭圆点集（映射后为正圆）
            virtual_circle_points = []
            for i in range(POINTS_PER_CIRCLE):
                angle = 2 * math.pi * i / POINTS_PER_CIRCLE
                x = virtual_center[0] + radius_x * math.cos(angle)
                y = virtual_center[1] + radius_y * math.sin(angle)
                virtual_circle_points.append((x, y))

            # 计算透视变换矩阵并映射坐标
            matrix = get_perspective_matrix(virtual_rect, sorted_corners)
            if matrix:
                mapped_points = transform_points(virtual_circle_points, matrix)
                int_points = [(int(round(x)), int(round(y))) for x, y in mapped_points]

                # 绘制圆形
                for (x, y) in int_points:
                    img.draw_circle(x, y, 1, color=(255, 0, 255), thickness=1)  # QQVGA: 缩小圆形点

                # 绘制圆心
                mapped_center = transform_points([virtual_center], matrix)
                if mapped_center:
                    cx, cy = map(int, map(round, mapped_center[0]))
                    img.draw_circle(cx, cy, 3, color=(0, 0, 255), thickness=1)

                # 发送坐标
                send_circle_points(int_points)

    # 绘制摄像头中心十字线
    img_width = img.width()
    img_height = img.height()
    center_x = img_width // 2
    center_y = img_height // 2

    # 绘制摄像头中心十字线 (白色)
    cross_size = 15  # QQVGA: 缩小十字线长度
    img.draw_line(center_x - cross_size, center_y, center_x + cross_size, center_y, color=(255, 255, 255), thickness=2)
    img.draw_line(center_x, center_y - cross_size, center_x, center_y + cross_size, color=(255, 255, 255), thickness=2)

    # 绘制中心点 (白色小圆)
    img.draw_circle(center_x, center_y, 2, color=(255, 255, 255), thickness=-1)  # QQVGA: 缩小中心点

    # 显示系统状态信息
    fps = clock.fps()
    img.draw_string(10, 10, "FPS: %.1f" % fps, color=(255, 255, 255))

    # 显示跟踪状态
    if prediction_mode:
        img.draw_string(10, 25, "MODE: PREDICT", color=(255, 0, 0))
        img.draw_string(10, 40, "LOST: %d/%d" % (lost_frames, max_lost_frames), color=(255, 0, 0))
    elif kalman_filter.initialized:
        img.draw_string(10, 25, "MODE: TRACK", color=(0, 255, 0))
        velocity = kalman_filter.get_velocity()
        img.draw_string(10, 40, "VEL: %.1f,%.1f" % (velocity[0], velocity[1]), color=(0, 255, 0))
    else:
        img.draw_string(10, 25, "MODE: SEARCH", color=(255, 255, 0))

    # 图像处理完成，无需LCD显示
    print("FPS: %.1f" % fps)
