'''
实验名称：最小矩形检测与全角度圆形校正
实验平台：OpenMV H7 Plus
说明：基于矩形主方向角精确校正透视变形，确保任意角度下圆形都显示为正圆
'''

import time, os, sys
import math
import sensor, image
from pyb import UART

# --------------------------- 硬件初始化 ---------------------------
# 串口初始化 (OpenMV使用pyb.UART)
uart = UART(3, 115200)  # 使用UART3，波特率115200
print("串口初始化完成")

# 发送测试指令验证连接
def test_uart_connection():
    """测试UART连接"""
    test_cmd = "1,1\r\n"  # 发送右移微调指令
    uart.write(test_cmd)
    print("发送测试指令: %s" % test_cmd.strip())

# 启动时测试连接
print("正在测试UART连接...")
test_uart_connection()

# 摄像头初始化
sensor.reset()
sensor.set_pixformat(sensor.RGB565)    # RGB565格式
sensor.set_framesize(sensor.QVGA)      # 320x240分辨率
sensor.skip_frames(time=2000)          # 等待摄像头稳定
sensor.set_auto_gain(False)            # 关闭自动增益
sensor.set_auto_whitebal(False)        # 关闭自动白平衡

# LCD初始化已移除

# --------------------------- 配置参数 ---------------------------
thresholds = [(0, 150)]        # 二值化阈值 - 降低阈值提高检测
MIN_AREA = 1500               # 最小面积阈值 - 降低最小面积
MAX_AREA = 30000             # 最大面积阈值 - 增加最大面积
MIN_ASPECT_RATIO = 0.2        # 最小宽高比 - 放宽比例限制
MAX_ASPECT_RATIO = 5.0        # 最大宽高比 - 放宽比例限制

BASE_RADIUS = 45              # 基础半径（虚拟坐标单位）
POINTS_PER_CIRCLE = 24        # 增加采样点使圆形更平滑
PURPLE_THRESHOLD = (20, 60, 15, 70, -70, -20)  # 紫色色块阈值

# 基础矩形比例（可根据实际需求调整）
RECT_WIDTH = 320
RECT_HEIGHT = 180
TARGET_ASPECT_RATIO = RECT_WIDTH / RECT_HEIGHT  # 目标宽高比

# --------------------------- 步进电机控制参数 ---------------------------
# 图像中心坐标
IMG_CENTER_X = 160  # 320/2
IMG_CENTER_Y = 120  # 240/2

# 追踪控制参数
TRACKING_DEADZONE = 5      # 死区范围（像素）- 适中死区避免震荡
CONTROL_INTERVAL = 300     # 控制间隔（毫秒）- 增加间隔避免过度控制

# 步进级别映射（根据偏差距离选择步进级别）- 精细化控制
STEP_LEVEL_1 = 5    # 微调：偏差 < 5像素
STEP_LEVEL_2 = 12   # 小步：偏差 < 12像素
STEP_LEVEL_3 = 25   # 中步：偏差 < 25像素
STEP_LEVEL_4 = 999  # 大步：偏差 >= 25像素

# 方向定义（与STM32保持一致）
DIR_RIGHT = 1  # 右移 (X轴正方向)
DIR_LEFT = 2   # 左移 (X轴负方向)
DIR_DOWN = 3   # 下移 (Y轴负方向)
DIR_UP = 4     # 上移 (Y轴正方向)

# --------------------------- 工具函数 ---------------------------
def calculate_distance(p1, p2):
    return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)

def calculate_center(points):
    if not points:
        return (0, 0)
    sum_x = sum(p[0] for p in points)
    sum_y = sum(p[1] for p in points)
    return (sum_x / len(points), sum_y / len(points))

def is_valid_rect(corners):
    edges = [calculate_distance(corners[i], corners[(i+1)%4]) for i in range(4)]

    ratio1 = edges[0] / max(edges[2], 0.1)
    ratio2 = edges[1] / max(edges[3], 0.1)
    valid_ratio = 0.5 < ratio1 < 1.5 and 0.5 < ratio2 < 1.5

    area = 0
    for i in range(4):
        x1, y1 = corners[i]
        x2, y2 = corners[(i+1) % 4]
        area += (x1 * y2 - x2 * y1)
    area = abs(area) / 2
    valid_area = MIN_AREA < area < MAX_AREA

    width = max(p[0] for p in corners) - min(p[0] for p in corners)
    height = max(p[1] for p in corners) - min(p[1] for p in corners)
    aspect_ratio = width / max(height, 0.1)
    valid_aspect = MIN_ASPECT_RATIO < aspect_ratio < MAX_ASPECT_RATIO

    return valid_ratio and valid_area and valid_aspect

def detect_purple_blobs(img):
    return img.find_blobs(
        [PURPLE_THRESHOLD],
        pixels_threshold=100,
        area_threshold=100,
        merge=True
    )

def send_circle_points(points):
    if not points:
        return
    count = len(points)
    msg = "$$C,%d," % count
    for x, y in points:
        msg += "%d,%d," % (x, y)
    msg = msg.rstrip(',') + "##"
    uart.write(msg + "\r\n")
    print("发送圆形点: %s" % msg)

def send_rect_center(center_x, center_y):
    """发送矩形中心坐标"""
    msg = "$$R,%d,%d##" % (center_x, center_y)
    uart.write(msg + "\r\n")
    print("发送矩形中心: %s" % msg)

# --------------------------- 步进电机控制函数 ---------------------------
def calculate_step_level(distance):
    """根据偏差距离计算步进级别 - 渐进式控制"""
    if distance < STEP_LEVEL_1:
        return 1  # 微调
    elif distance < STEP_LEVEL_2:
        return 1  # 小步 - 改为1级，避免跳跃
    elif distance < STEP_LEVEL_3:
        return 2  # 中步 - 改为2级，渐进增加
    else:
        return 2  # 大步 - 限制最大为2级，避免过大转动

def send_motor_command(direction, level):
    """发送步进电机控制指令"""
    # 指令格式: "方向,级别\r\n"
    cmd = "%d,%d\r\n" % (direction, level)
    uart.write(cmd)
    print("电机指令: %s" % cmd.strip())  # 添加调试输出

def control_stepper_motor(target_x, target_y):
    """根据目标位置控制步进电机 - 平滑控制"""
    global last_error_x, last_error_y

    # 计算偏差
    error_x = target_x - IMG_CENTER_X
    error_y = target_y - IMG_CENTER_Y

    # 计算偏差距离
    distance_x = abs(error_x)
    distance_y = abs(error_y)

    # 检查是否在死区内
    if distance_x <= TRACKING_DEADZONE and distance_y <= TRACKING_DEADZONE:
        print("在死区内")
        return

    # 平滑控制：如果偏差变化太大，限制单次移动量
    max_change = 20  # 最大偏差变化限制
    if abs(error_x - last_error_x) > max_change:
        if error_x > last_error_x:
            error_x = last_error_x + max_change
        else:
            error_x = last_error_x - max_change

    if abs(error_y - last_error_y) > max_change:
        if error_y > last_error_y:
            error_y = last_error_y + max_change
        else:
            error_y = last_error_y - max_change

    # 更新历史偏差
    last_error_x = error_x
    last_error_y = error_y

    # 重新计算距离
    distance_x = abs(error_x)
    distance_y = abs(error_y)

    print("偏差: X=%d Y=%d" % (error_x, error_y))

    # 优先控制偏差更大的轴，避免同时发送指令
    if distance_x > distance_y and distance_x > TRACKING_DEADZONE:
        # X轴偏差更大，优先控制X轴
        level_x = calculate_step_level(distance_x)
        if error_x > 0:
            # 目标在右侧，需要左移（修正方向）
            send_motor_command(DIR_LEFT, level_x)
        else:
            # 目标在左侧，需要右移（修正方向）
            send_motor_command(DIR_RIGHT, level_x)
    elif distance_y > TRACKING_DEADZONE:
        # Y轴偏差更大或X轴已在死区内，控制Y轴
        level_y = calculate_step_level(distance_y)
        if error_y > 0:
            # 目标在下方，需要下移
            send_motor_command(DIR_DOWN, level_y)
        else:
            # 目标在上方，需要上移
            send_motor_command(DIR_UP, level_y)

def get_perspective_matrix(src_pts, dst_pts):
    """计算透视变换矩阵"""
    A = []
    B = []
    for i in range(4):
        x, y = src_pts[i]
        u, v = dst_pts[i]
        A.append([x, y, 1, 0, 0, 0, -u*x, -u*y])
        A.append([0, 0, 0, x, y, 1, -v*x, -v*y])
        B.append(u)
        B.append(v)

    # 高斯消元求解矩阵
    n = 8
    for i in range(n):
        max_row = i
        for j in range(i, len(A)):
            if abs(A[j][i]) > abs(A[max_row][i]):
                max_row = j
        A[i], A[max_row] = A[max_row], A[i]
        B[i], B[max_row] = B[max_row], B[i]

        pivot = A[i][i]
        if abs(pivot) < 1e-8:
            return None
        for j in range(i, n):
            A[i][j] /= pivot
        B[i] /= pivot

        for j in range(len(A)):
            if j != i and A[j][i] != 0:
                factor = A[j][i]
                for k in range(i, n):
                    A[j][k] -= factor * A[i][k]
                B[j] -= factor * B[i]

    return [
        [B[0], B[1], B[2]],
        [B[3], B[4], B[5]],
        [B[6], B[7], 1.0]
    ]

def transform_points(points, matrix):
    """应用透视变换将虚拟坐标映射到原始图像坐标"""
    transformed = []
    for (x, y) in points:
        x_hom = x * matrix[0][0] + y * matrix[0][1] + matrix[0][2]
        y_hom = x * matrix[1][0] + y * matrix[1][1] + matrix[1][2]
        w_hom = x * matrix[2][0] + y * matrix[2][1] + matrix[2][2]
        if abs(w_hom) > 1e-8:
            transformed.append((x_hom / w_hom, y_hom / w_hom))
    return transformed

def sort_corners(corners):
    """将矩形角点按左上、右上、右下、左下顺序排序"""
    center = calculate_center(corners)
    sorted_corners = sorted(corners, key=lambda p: math.atan2(p[1]-center[1], p[0]-center[0]))

    # 调整顺序为左上、右上、右下、左下
    if len(sorted_corners) == 4:
        # 找到最接近左上角的点
        left_top = min(sorted_corners, key=lambda p: p[0]+p[1])
        index = sorted_corners.index(left_top)
        sorted_corners = sorted_corners[index:] + sorted_corners[:index]
    return sorted_corners

def get_rectangle_orientation(corners):
    """计算矩形的主方向角（水平边与x轴的夹角）"""
    # 假设排序后的角点顺序为：左上、右上、右下、左下
    if len(corners) != 4:
        return 0

    # 计算上边和右边的向量
    top_edge = (corners[1][0] - corners[0][0], corners[1][1] - corners[0][1])
    right_edge = (corners[2][0] - corners[1][0], corners[2][1] - corners[1][1])

    # 选择较长的边作为主方向
    if calculate_distance(corners[0], corners[1]) > calculate_distance(corners[1], corners[2]):
        main_edge = top_edge
    else:
        main_edge = right_edge

    # 计算主方向角（弧度）
    angle = math.atan2(main_edge[1], main_edge[0])
    return angle

print("OpenMV透视校正系统启动完成")
print("检测参数:")
print("- 二值化阈值: (0, 150)")
print("- 矩形面积范围: %d - %d" % (MIN_AREA, MAX_AREA))
print("- 圆形采样点数: %d" % POINTS_PER_CIRCLE)

# --------------------------- 主循环 ---------------------------
clock = time.clock()
last_control_time = time.ticks_ms()  # 控制时间记录
last_error_x = 0  # 上次X轴偏差
last_error_y = 0  # 上次Y轴偏差

while True:
    clock.tick()
    img = sensor.snapshot()

    # 移除定期测试指令，提高性能

    # 图像处理
    gray_img = img.to_grayscale()
    binary_img = gray_img.binary(thresholds)
    binary_img.erode(1)
    binary_img.dilate(3)

    # 检测紫色色块
    purple_blobs = detect_purple_blobs(img)
    for blob in purple_blobs:
        img.draw_rectangle(blob.rect(), color=(255, 0, 255), thickness=1)
        img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 255), thickness=1)

    # 检测所有矩形并筛选出最小的一个
    min_area = float('inf')
    smallest_rect = None

    for r in binary_img.find_rects(threshold=8000):  # 降低阈值提高检测
        corners = r.corners()
        if is_valid_rect(corners):
            # 计算矩形面积
            area = 0
            for i in range(4):
                x1, y1 = corners[i]
                x2, y2 = corners[(i+1) % 4]
                area += (x1 * y2 - x2 * y1)
            area = abs(area) / 2

            # 更新最小矩形
            if area < min_area:
                min_area = area
                smallest_rect = corners

    # 只处理最小的矩形
    if smallest_rect:
        # 对矩形角点进行排序
        sorted_corners = sort_corners(smallest_rect)

        # 计算矩形中心点
        rect_center = calculate_center(sorted_corners)
        center_x, center_y = int(rect_center[0]), int(rect_center[1])

        # 绘制矩形边框和角点
        for i in range(4):
            x1, y1 = sorted_corners[i]
            x2, y2 = sorted_corners[(i+1) % 4]
            img.draw_line(x1, y1, x2, y2, color=(255, 0, 0), thickness=1)
        for p in sorted_corners:
            img.draw_circle(p[0], p[1], 5, color=(0, 255, 0), thickness=1)

        # 绘制矩形中心点 - 用大圆圈标出
        img.draw_circle(center_x, center_y, 8, color=(255, 255, 0), thickness=2)  # 黄色外圈
        img.draw_circle(center_x, center_y, 4, color=(255, 255, 0), thickness=-1) # 黄色实心圆

        # 绘制十字线标记中心
        img.draw_line(center_x-10, center_y, center_x+10, center_y, color=(255, 255, 0), thickness=1)
        img.draw_line(center_x, center_y-10, center_x, center_y+10, color=(255, 255, 0), thickness=1)

        # 注释掉矩形中心坐标发送，避免干扰电机控制
        # send_rect_center(center_x, center_y)

        # 步进电机自动追踪控制
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, last_control_time) >= CONTROL_INTERVAL:
            # 控制步进电机使矩形中心与图像中心重合
            control_stepper_motor(center_x, center_y)
            last_control_time = current_time

        # 计算矩形主方向角
        angle = get_rectangle_orientation(sorted_corners)

        # 计算矩形实际宽高
        width = calculate_distance(sorted_corners[0], sorted_corners[1])
        height = calculate_distance(sorted_corners[1], sorted_corners[2])
        actual_aspect = width / max(height, 0.1)

        # 确定矩形是横向还是纵向（考虑旋转）
        is_horizontal = actual_aspect >= 1.0

        # 构建虚拟矩形（根据方向调整）
        if is_horizontal:
            virtual_rect = [
                (0, 0),
                (RECT_WIDTH, 0),
                (RECT_WIDTH, RECT_HEIGHT),
                (0, RECT_HEIGHT)
            ]
        else:
            virtual_rect = [
                (0, 0),
                (RECT_HEIGHT, 0),
                (RECT_HEIGHT, RECT_WIDTH),
                (0, RECT_WIDTH)
            ]

        # 计算校正半径（基于实际宽高比）
        if is_horizontal:
            radius_x = BASE_RADIUS
            radius_y = BASE_RADIUS / actual_aspect
        else:
            radius_x = BASE_RADIUS * actual_aspect
            radius_y = BASE_RADIUS

        # 计算虚拟矩形中心
        virtual_center = (RECT_WIDTH/2, RECT_HEIGHT/2) if is_horizontal else (RECT_HEIGHT/2, RECT_WIDTH/2)

        # 在虚拟矩形中生成椭圆点集（映射后为正圆）
        virtual_circle_points = []
        for i in range(POINTS_PER_CIRCLE):
            angle = 2 * math.pi * i / POINTS_PER_CIRCLE
            x = virtual_center[0] + radius_x * math.cos(angle)
            y = virtual_center[1] + radius_y * math.sin(angle)
            virtual_circle_points.append((x, y))

        # 计算透视变换矩阵并映射坐标
        matrix = get_perspective_matrix(virtual_rect, sorted_corners)
        if matrix:
            mapped_points = transform_points(virtual_circle_points, matrix)
            int_points = [(int(round(x)), int(round(y))) for x, y in mapped_points]

            # 绘制圆形
            for (x, y) in int_points:
                img.draw_circle(x, y, 2, color=(255, 0, 255), thickness=2)

            # 绘制圆心
            mapped_center = transform_points([virtual_center], matrix)
            if mapped_center:
                cx, cy = map(int, map(round, mapped_center[0]))
                img.draw_circle(cx, cy, 3, color=(0, 0, 255), thickness=1)

            # 注释掉圆形坐标发送，避免干扰电机控制
            # send_circle_points(int_points)

    # 绘制摄像头中心十字线
    img_width = img.width()
    img_height = img.height()
    center_x = img_width // 2
    center_y = img_height // 2

    # 绘制摄像头中心十字线 (白色)
    cross_size = 20  # 十字线长度
    img.draw_line(center_x - cross_size, center_y, center_x + cross_size, center_y, color=(255, 255, 255), thickness=2)
    img.draw_line(center_x, center_y - cross_size, center_x, center_y + cross_size, color=(255, 255, 255), thickness=2)

    # 绘制中心点 (白色小圆)
    img.draw_circle(center_x, center_y, 3, color=(255, 255, 255), thickness=-1)

    # 显示FPS和追踪状态
    fps = clock.fps()
    img.draw_string(10, 10, "FPS: %.1f" % fps, color=(255, 255, 255))

    # 显示追踪状态信息
    if smallest_rect:
        rect_center = calculate_center(smallest_rect)
        error_x = int(rect_center[0]) - IMG_CENTER_X
        error_y = int(rect_center[1]) - IMG_CENTER_Y
        distance = int(math.sqrt(error_x*error_x + error_y*error_y))

        # 显示偏差信息
        img.draw_string(10, 30, "Error: X=%d Y=%d" % (error_x, error_y), color=(255, 255, 255))
        img.draw_string(10, 50, "Distance: %d" % distance, color=(255, 255, 255))

        # 显示追踪状态
        if distance <= TRACKING_DEADZONE:
            img.draw_string(10, 70, "Status: LOCKED", color=(0, 255, 0))
        else:
            img.draw_string(10, 70, "Status: TRACKING", color=(255, 255, 0))
    else:
        img.draw_string(10, 30, "Status: NO TARGET", color=(255, 0, 0))

    # 减少打印输出，提高性能
    # print("FPS: %.1f" % fps)
