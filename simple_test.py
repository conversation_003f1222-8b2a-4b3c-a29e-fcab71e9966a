# 简单矩形检测测试 - 专门解决面积过大问题

import sensor, image, time

# 初始化传感器
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

def detect_rectangle_edges(img):
    """使用边缘检测找矩形框"""
    print("\n=== 边缘检测矩形 ===")
    
    try:
        # 转换为灰度图
        gray = img.to_grayscale()
        
        # 边缘检测
        edges = gray.find_edges(image.EDGE_CANNY, threshold=(50, 80))
        
        # 查找矩形
        rectangles = edges.find_rects(threshold=1000)
        
        print("找到 {} 个矩形".format(len(rectangles)))
        
        valid_rects = []
        for i, rect in enumerate(rectangles):
            area = rect.w() * rect.h()
            cx = rect.x() + rect.w() // 2
            cy = rect.y() + rect.h() // 2
            
            print("矩形{}: 面积={} 位置=({},{}) 尺寸={}x{}".format(
                i+1, area, cx, cy, rect.w(), rect.h()))
            
            # 面积过滤：排除太小和整个图像
            if 1000 <= area <= 50000:
                # 尺寸过滤：不能是整个图像大小
                if rect.w() < 300 and rect.h() < 220:
                    valid_rects.append(rect)
                    
                    # 绘制绿色框
                    img.draw_rectangle(rect, color=(0, 255, 0), thickness=3)
                    img.draw_cross(cx, cy, color=(0, 255, 0), size=15)
                    img.draw_string(cx-20, cy-30, "RECT", color=(0, 255, 0))
                    
                    print("✓ 有效矩形: 中心({},{}) 面积={}".format(cx, cy, area))
                else:
                    print("✗ 尺寸过大: {}x{}".format(rect.w(), rect.h()))
            else:
                print("✗ 面积不符: {}".format(area))
        
        return valid_rects
        
    except Exception as e:
        print("边缘检测失败:", e)
        return []

def detect_black_blobs_filtered(img):
    """检测黑色区域但排除背景"""
    print("\n=== 过滤黑色检测 ===")
    
    # 使用严格的黑色阈值
    black_threshold = (0, 60, -128, 127, -128, 127)
    
    blobs = img.find_blobs([black_threshold], 
                          pixels_threshold=200, 
                          area_threshold=200,
                          merge=True)
    
    print("检测到 {} 个黑色区域".format(len(blobs)))
    
    valid_blobs = []
    for i, blob in enumerate(blobs):
        print("区域{}: 面积={} 位置=({},{}) 尺寸={}x{}".format(
            i+1, blob.pixels(), blob.cx(), blob.cy(), blob.w(), blob.h()))
        
        # 严格过滤条件
        area_ok = 500 <= blob.pixels() <= 20000  # 面积范围
        size_ok = blob.w() < 280 and blob.h() < 200  # 尺寸限制
        not_background = blob.pixels() < 60000  # 不是背景
        
        print("  面积检查: {} (500-20000)".format("✓" if area_ok else "✗"))
        print("  尺寸检查: {} (w<280, h<200)".format("✓" if size_ok else "✗"))
        print("  背景检查: {} (<60000)".format("✓" if not_background else "✗"))
        
        if area_ok and size_ok and not_background:
            valid_blobs.append(blob)
            
            # 绘制黄色框
            img.draw_rectangle(blob.rect(), color=(255, 255, 0), thickness=3)
            img.draw_cross(blob.cx(), blob.cy(), color=(255, 255, 0), size=15)
            img.draw_string(blob.cx()-20, blob.cy()-30, "BLOB", color=(255, 255, 0))
            
            print("✓ 有效区域: 中心({},{}) 面积={}".format(
                blob.cx(), blob.cy(), blob.pixels()))
        else:
            print("✗ 被过滤")
    
    return valid_blobs

def detect_inner_rectangles(img):
    """检测内部矩形 - 寻找矩形框内的空白区域"""
    print("\n=== 内部矩形检测 ===")
    
    # 检测白色/亮色区域 (矩形框内部)
    white_threshold = (200, 255, -128, 127, -128, 127)
    
    blobs = img.find_blobs([white_threshold], 
                          pixels_threshold=1000, 
                          area_threshold=1000,
                          merge=True)
    
    print("检测到 {} 个亮色区域".format(len(blobs)))
    
    valid_inner = []
    for i, blob in enumerate(blobs):
        print("亮区{}: 面积={} 位置=({},{}) 尺寸={}x{}".format(
            i+1, blob.pixels(), blob.cx(), blob.cy(), blob.w(), blob.h()))
        
        # 寻找合理大小的内部区域
        if 2000 <= blob.pixels() <= 40000:
            # 检查是否接近矩形
            aspect_ratio = max(blob.w(), blob.h()) / max(min(blob.w(), blob.h()), 1)
            if aspect_ratio <= 3.0:
                valid_inner.append(blob)
                
                # 绘制蓝色框
                img.draw_rectangle(blob.rect(), color=(0, 0, 255), thickness=2)
                img.draw_cross(blob.cx(), blob.cy(), color=(0, 0, 255), size=10)
                img.draw_string(blob.cx()-20, blob.cy()+20, "INNER", color=(0, 0, 255))
                
                print("✓ 内部区域: 中心({},{}) 面积={} 长宽比={:.1f}".format(
                    blob.cx(), blob.cy(), blob.pixels(), aspect_ratio))
    
    return valid_inner

# 主循环
print("简单矩形检测测试启动")
frame_count = 0

while True:
    img = sensor.snapshot()
    frame_count += 1
    
    # 每5帧进行一次检测
    if frame_count % 5 == 0:
        print("\n" + "="*60)
        print("帧 {}".format(frame_count))
        
        # 方法1: 边缘检测矩形
        edge_rects = detect_rectangle_edges(img)
        
        # 方法2: 过滤的黑色检测
        black_blobs = detect_black_blobs_filtered(img)
        
        # 方法3: 内部区域检测
        inner_rects = detect_inner_rectangles(img)
        
        # 总结
        total_found = len(edge_rects) + len(black_blobs) + len(inner_rects)
        print("\n总结: 边缘={} 黑色={} 内部={} 总计={}".format(
            len(edge_rects), len(black_blobs), len(inner_rects), total_found))
        
        if total_found > 0:
            print("✓ 检测成功！")
        else:
            print("✗ 未检测到有效矩形")
    
    # 显示状态
    img.draw_string(10, 10, "Frame: {}".format(frame_count), color=(255, 255, 255))
    img.draw_string(10, 30, "Simple Test", color=(255, 255, 0))
    
    time.sleep_ms(200)  # 控制帧率
