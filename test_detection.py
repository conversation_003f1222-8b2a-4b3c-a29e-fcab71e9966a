# OpenMV 矩形检测调试版本
# 专门用于调试和测试矩形检测功能

import sensor, image, time

# 初始化传感器
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

def test_all_detection_methods(img):
    """测试所有检测方法"""
    print("\n=== 开始全面检测测试 ===")
    
    # 方法1: LAB色彩空间检测
    print("\n--- 方法1: LAB色彩空间检测 ---")
    test_lab_detection(img)
    
    # 方法2: RGB色彩空间检测
    print("\n--- 方法2: RGB色彩空间检测 ---")
    test_rgb_detection(img)
    
    # 方法3: 灰度阈值检测
    print("\n--- 方法3: 灰度阈值检测 ---")
    test_gray_detection(img)
    
    # 方法4: 边缘检测
    print("\n--- 方法4: 边缘检测 ---")
    test_edge_detection(img)

def test_lab_detection(img):
    """测试LAB色彩空间检测"""
    thresholds = [
        (0, 30, -128, 127, -128, 127),    # 很严格
        (0, 50, -128, 127, -128, 127),    # 中等
        (0, 80, -128, 127, -128, 127),    # 宽松
        (0, 100, -128, 127, -128, 127),   # 很宽松
    ]
    
    for i, threshold in enumerate(thresholds):
        blobs = img.find_blobs([threshold], 
                              pixels_threshold=50, 
                              area_threshold=50,
                              merge=True)
        
        print("LAB阈值{}: {} 个区域".format(i+1, len(blobs)))
        
        for j, blob in enumerate(blobs):
            if j < 3:  # 只显示前3个
                print("  区域{}: 面积={} 位置=({},{}) 尺寸={}x{}".format(
                    j+1, blob.pixels(), blob.cx(), blob.cy(), blob.w(), blob.h()))
                
                # 绘制不同颜色的框
                colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
                img.draw_rectangle(blob.rect(), color=colors[i], thickness=1)

def test_rgb_detection(img):
    """测试RGB色彩空间检测"""
    # 转换为RGB并检测暗色区域
    try:
        # 直接在RGB565图像上检测暗色
        # RGB565中黑色的范围
        rgb_thresholds = [
            (0, 30, 0, 30, 0, 30),        # 很暗
            (0, 50, 0, 50, 0, 50),        # 暗
            (0, 80, 0, 80, 0, 80),        # 较暗
        ]
        
        for i, threshold in enumerate(rgb_thresholds):
            # 注意：OpenMV的RGB检测可能不支持，这里做尝试
            print("RGB阈值{}: 尝试检测...".format(i+1))
            
    except Exception as e:
        print("RGB检测不支持:", e)

def test_gray_detection(img):
    """测试灰度阈值检测"""
    try:
        # 转换为灰度图
        gray = img.to_grayscale()
        
        # 不同的二值化阈值
        thresholds = [30, 50, 80, 100, 120]
        
        for threshold in thresholds:
            # 二值化
            binary = gray.binary([(0, threshold)])
            
            # 查找blob
            blobs = binary.find_blobs([(255, 255)], 
                                    pixels_threshold=100, 
                                    area_threshold=100,
                                    merge=True)
            
            print("灰度阈值{}: {} 个区域".format(threshold, len(blobs)))
            
            for j, blob in enumerate(blobs):
                if j < 2:  # 只显示前2个
                    print("  区域{}: 面积={} 位置=({},{})".format(
                        j+1, blob.pixels(), blob.cx(), blob.cy()))
                    
                    # 绘制青色框
                    img.draw_rectangle(blob.rect(), color=(0, 255, 255), thickness=1)
                    
    except Exception as e:
        print("灰度检测失败:", e)

def test_edge_detection(img):
    """测试边缘检测"""
    try:
        # 转换为灰度图
        gray = img.to_grayscale()
        
        # 边缘检测
        edges = gray.find_edges(image.EDGE_CANNY, threshold=(50, 80))
        
        # 查找矩形
        rectangles = edges.find_rects(threshold=500)
        
        print("边缘检测: {} 个矩形".format(len(rectangles)))
        
        for i, rect in enumerate(rectangles):
            if i < 3:  # 只显示前3个
                area = rect.w() * rect.h()
                cx = rect.x() + rect.w() // 2
                cy = rect.y() + rect.h() // 2
                
                print("  矩形{}: 面积={} 位置=({},{}) 尺寸={}x{}".format(
                    i+1, area, cx, cy, rect.w(), rect.h()))
                
                # 绘制紫色框
                img.draw_rectangle(rect, color=(255, 0, 255), thickness=2)
                img.draw_cross(cx, cy, color=(255, 0, 255), size=8)
                
    except Exception as e:
        print("边缘检测失败:", e)

def simple_black_detection(img):
    """最简单的黑色检测"""
    print("\n=== 简单黑色检测 ===")
    
    # 最宽松的黑色阈值
    black_threshold = (0, 100, -128, 127, -128, 127)
    
    blobs = img.find_blobs([black_threshold], 
                          pixels_threshold=50, 
                          area_threshold=50,
                          merge=True)
    
    print("检测到 {} 个黑色区域".format(len(blobs)))
    
    for i, blob in enumerate(blobs):
        print("区域{}: 面积={} 紧密度={:.2f} 位置=({},{}) 尺寸={}x{}".format(
            i+1, blob.pixels(), blob.compactness(), 
            blob.cx(), blob.cy(), blob.w(), blob.h()))
        
        # 绘制白色框，容易看见
        img.draw_rectangle(blob.rect(), color=(255, 255, 255), thickness=2)
        img.draw_cross(blob.cx(), blob.cy(), color=(255, 255, 255), size=10)
        
        # 添加文字标签
        img.draw_string(blob.cx()-10, blob.cy()-20, str(i+1), color=(255, 255, 255))

# 主循环
print("矩形检测调试程序启动")
print("将显示多种检测方法的结果")

frame_count = 0

while True:
    img = sensor.snapshot()
    frame_count += 1
    
    # 每10帧进行一次详细检测
    if frame_count % 10 == 0:
        print("\n" + "="*50)
        print("帧 {}".format(frame_count))
        
        # 简单检测
        simple_black_detection(img)
        
        # 如果需要更详细的测试，取消下面的注释
        # test_all_detection_methods(img)
    
    # 显示帧数
    img.draw_string(10, 10, "Frame: {}".format(frame_count), color=(255, 255, 255))
    img.draw_string(10, 30, "Debug Mode", color=(255, 255, 0))
    
    time.sleep_ms(100)  # 控制帧率
